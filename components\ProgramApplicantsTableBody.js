import { TableRow, TableCell } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye } from "lucide-react";

export default function ProgramApplicantsTableBody({ applicants = [], getBadgeVariant, onViewDetails }) {
  if (!applicants || applicants.length === 0) {
    return (
      <TableRow>
        <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
          No applicants found. Try adjusting your filters.
        </TableCell>
      </TableRow>
    );
  }

  return (
    <>
      {applicants.map((applicant) => (
        <TableRow key={applicant._id} className="border-b-slate-700 hover:bg-slate-800/40">
          <TableCell className="font-medium">
            {applicant.formData.name || "N/A"}
          </TableCell>
          
          <TableCell>
            <div className="flex flex-col gap-1">
              <span className="text-sm ">{applicant.formData.phone || "N/A"}</span>
            </div>
          </TableCell>
          
          <TableCell>
            {applicant.formData.ward || "N/A"}
          </TableCell>
          
          <TableCell>
            <Badge className={getBadgeVariant(applicant.status)}>
              {applicant.status?.charAt(0).toUpperCase() + applicant.status?.slice(1) || "Unknown"}
            </Badge>
          </TableCell>
          
          <TableCell>
            <Button
            
              size="sm"
              className="flex gap-1 items-center hover:bg-blue-600 text-white bg-blue-900 border-blue-600"
              onClick={() => onViewDetails(applicant._id)}
            >
              <Eye className="h-4 w-4" /> View
            </Button>
          </TableCell>
        </TableRow>
      ))}
    </>
  );
}