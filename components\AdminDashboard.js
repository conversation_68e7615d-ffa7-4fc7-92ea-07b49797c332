"use client"

import { useState } from "react"
import Link from "next/link"
import { LayoutDashboard,UserPlus, File, ClipboardList, Users, Bar<PERSON>hart, User } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import Ward<PERSON><PERSON> from "./WardChart"
import ExportBeneficiariesModal from "./ExportBeneficiariesModal"
import AdminOnly from "./AdminOnly" 
const navItems = [
  { href: "/programs/programcreation", label: "Create New Program", icon: ClipboardList },
  { id: "export-modal", label: "Export Beneficiaries", icon: File, isModal: true },
  { href: "/applicants", label: "View Applications", icon: Users },
  { href: "/manageusers", label: "Add New User", icon: UserPlus },
]

export default function AdminDashboard() {
  const [isModalOpen, setIsModalOpen] = useState(false)

  const openModal = () => {
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
  }

  return ( 

    <AdminOnly> 
    <>
    
      <Card className="border-2 shadow-2xl bg-background dark:border-ring">
        <CardHeader className="border-b-2 border-ring">  
          <CardTitle className="font-bold mb-3">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4">
            {navItems.map((item) => (
              item.isModal ? (
                <div 
                  key={item.id} 
                  onClick={openModal}
                  className="shadow-md p-6 rounded-xl transition-colors bg-card text-white flex flex-col items-center justify-center gap-4 cursor-pointer hover:bg-gray-400 dark:hover:bg-slate-800"
                >
                  <item.icon size={28} />
                  <h2 className="text-sm md:text-md font-mono text-center font-semibold">{item.label}</h2>
                </div>
              ) : (
                <Link key={item.href} href={item.href}>
                  <div className="shadow-md p-6 rounded-xl transition-colors bg-card text-white flex flex-col items-center justify-center gap-4 cursor-pointer hover:bg-gray-400 dark:hover:bg-slate-800">
                    <item.icon size={28} />
                    <h2 className="text-sm md:text-md font-mono text-center font-semibold">{item.label}</h2>
                  </div>
                </Link>
              )
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Export Beneficiaries Modal */}
      <ExportBeneficiariesModal isOpen={isModalOpen} onClose={closeModal} />
      
      <div className="mt-8">
        <WardChart/>
      </div>
    </>

    </AdminOnly>
  )
}