// components/profile/ProfileForm.jsx
"use client"
import { useState, useEffect } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { UserIcon, BookOpenIcon, PhoneIcon, MailIcon } from "lucide-react"
import { toast } from "sonner"
import { z } from "zod"

// Profile update validation schema
const profileUpdateSchema = z
  .object({
    name: z
      .string()
      .min(2, "Name must be at least 2 characters")
      .max(100, "Name must be less than 100 characters")
      .optional(),
    email: z.string().email("Invalid email format").optional(),
    phone: z
      .string()
      .regex(/^\+?[0-9]{8,15}$/, "Phone number must be between 8-15 digits")
      .optional(),
    bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
  })
  .refine((data) => Object.keys(data).length > 0, {
    message: "At least one field must be provided for update",
  })

export default function ProfileForm({ profileData, api }) {
  const queryClient = useQueryClient()
  const [profileErrors, setProfileErrors] = useState({})
  
  // Initialize form state
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    bio: "",
  })

  // Update form data when profile data loads
  useEffect(() => {
    if (profileData) {
      setFormData({
        name: profileData.name || "",
        email: profileData.email || "",
        phone: profileData.phone || "",
        bio: profileData.bio || "",
      })
    }
  }, [profileData])

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: api.updateProfile,
    onSuccess: () => {
      toast.success("Profile updated successfully", {
        description: "Your profile information has been saved",
      })
      queryClient.invalidateQueries({ queryKey: ["profile"] })
      setProfileErrors({})
    },
    onError: (error) => {
      if (error.name === "ZodError") {
        // Handle Zod validation errors
        const formattedErrors = {}
        error.errors.forEach((err) => {
          formattedErrors[err.path[0]] = err.message
        })
        setProfileErrors(formattedErrors)
        toast.error("Validation failed", {
          description: "Please check the form for errors",
        })
      } else {
        toast.error("Update failed", {
          description: error.response?.data?.error || "Failed to update profile",
        })
      }
    },
  })

  // Handle profile form input changes
  const handleProfileChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))

    // Clear error for this field when user starts typing
    if (profileErrors[name]) {
      setProfileErrors((prev) => ({ ...prev, [name]: undefined }))
    }
  }

  // Save profile changes
  const saveProfileChanges = (e) => {
    e.preventDefault()

    try {
      // Validate form data before submitting
      profileUpdateSchema.parse(formData)
      updateProfileMutation.mutate(formData)
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Format and display validation errors
        const formattedErrors = {}
        error.errors.forEach((err) => {
          formattedErrors[err.path[0]] = err.message
        })
        setProfileErrors(formattedErrors)
        toast.error("Validation failed", {
          description: "Please check the form for errors",
        })
      }
    }
  }

  return (
    <form onSubmit={saveProfileChanges}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Bento Grid: Personal Info Card */}
        <div className="bg-card rounded-xl shadow-sm p-6 border">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <UserIcon className="mr-2 h-5 w-5" />
            Personal Information
          </h2>
          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium mb-1">
                Full Name
              </label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleProfileChange}
                className={`w-full ${profileErrors.name ? "border-red-500" : ""}`}
              />
              {profileErrors.name && <p className="text-red-500 text-xs mt-1">{profileErrors.name}</p>}
              <p className="text-xs text-muted-foreground mt-1">Must be 2-100 characters</p>
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-1">
                Email
              </label>
              <div className="relative">
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleProfileChange}
                  placeholder="Add email to enable password change and recovery"
                  className={`w-full pl-9 ${profileErrors.email ? "border-red-500" : ""}`}
                />
                <MailIcon className="absolute text-white left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" />
                {profileErrors.email && <p className="text-red-500 text-xs mt-1">{profileErrors.email}</p>}
              </div>
            </div>
            <div>
              <label htmlFor="phone" className="block text-sm font-medium mb-1">
                Phone Number
              </label>
              <div className="relative">
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleProfileChange}
                  className={`w-full pl-9 ${profileErrors.phone ? "border-red-500" : ""}`}
                />
                <PhoneIcon className="absolute left-3 top-5 transform -translate-y-1/2 h-4 w-4" />
                {profileErrors.phone && <p className="text-red-500 text-xs mt-1">{profileErrors.phone}</p>}
                <p className="text-xs text-muted-foreground mt-1">
                  Must be 8-15 digits, can include + prefix
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Bento Grid: Role & Bio Card */}
        <div className="bg-card rounded-xl shadow-sm p-6 border flex flex-col">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <BookOpenIcon className="mr-2 h-5 w-5" />
            About You
          </h2>
          <div className="space-y-4 flex-1">
            <div>
              <label htmlFor="role" className="block text-sm font-medium mb-1">
                Username
              </label>
              <Input
                id="role"
                value={profileData?.username || ""}
                readOnly
                disabled
                className="w-full cursor-not-allowed"
              />
              <p className="text-xs text-blue-500 mt-1">Your Username cannot be changed</p>
            </div>

            <div className="flex-1">
              <label htmlFor="bio" className="block text-sm font-medium mb-1">
                Bio
              </label>
              <Textarea
                id="bio"
                name="bio"
                value={formData.bio}
                onChange={handleProfileChange}
                className={`w-full h-32 ${profileErrors.bio ? "border-red-500" : ""}`}
                placeholder="Tell us about yourself..."
              />
              {profileErrors.bio && <p className="text-red-500 text-xs mt-1">{profileErrors.bio}</p>}
              <p className="text-xs text-muted-foreground mt-1">Maximum 500 characters</p>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-4 flex justify-center">
        <Button
          type="submit"
          disabled={updateProfileMutation.isPending}
          className="w-full md:w-1/3 mb-5 text-white bg-slate-700 hover:bg-slate-600"
        >
          {updateProfileMutation.isPending ? (
            <>
              <div className="w-4 h-4 border-2 border-t-white border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mr-2"></div>
              Saving...
            </>
          ) : (
            "Save Changes"
          )}
        </Button>
      </div>
    </form>
  )
}