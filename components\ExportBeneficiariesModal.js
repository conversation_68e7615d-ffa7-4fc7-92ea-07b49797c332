"use client"

import { useEffect, useState } from "react"
import { FileDown, X, Users, ChevronLeft, ChevronRight } from "lucide-react"
import axios from "axios"
import { toast } from "sonner"
import { useQuery } from "@tanstack/react-query"
import ExportSelectedApplicantsButton from "./ExportSelectedApplicantsButton"
import { Button } from "@/components/ui/button"

export default function ExportBeneficiariesModal({ isOpen, onClose }) {
  // Pagination state
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // Using React Query for data fetching with pagination
  const { 
    data, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ['programs2', page, limit],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      }).toString();
      
      const response = await axios.get(`/api/programs/fetch?${params}`)
      if (response.data && Array.isArray(response.data.programs)) {
        // Set total pages from pagination data
        if (response.data.pagination && response.data.pagination.pages) {
          setTotalPages(response.data.pagination.pages);
        }
        return response.data.programs;
      }
      throw new Error("Failed to fetch programs")
    },
    enabled: false, // Don't fetch on component mount
    keepPreviousData: true, // Keep previous data while fetching new data
  })

  // Refetch when modal opens
  useEffect(() => {
    if (isOpen) {
      refetch()
    }
  }, [isOpen, refetch])

  // Refetch when pagination changes
  useEffect(() => {
    if (isOpen) {
      refetch()
    }
  }, [page, limit, isOpen, refetch])

  // Error handling
  useEffect(() => {
    if (error) {
      toast.error("Error loading programs", {
        description: error.message || "Please try again later"
      })
    }
  }, [error])

  // Format date function
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Pagination handlers
  const handlePreviousPage = () => {
    setPage(prevPage => Math.max(1, prevPage - 1));
  };

  const handleNextPage = () => {
    setPage(prevPage => Math.min(totalPages, prevPage + 1));
  };

  return (
    <div className={`modal ${isOpen ? 'modal-open backdrop-blur-sm bg-opacity-0' : ''}`} onClick={onClose}>
      <div 
        className="modal-box max-w-4xl max-h-10/12 bg-card border border-slate-700 text-white"
        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside
      >
        <div className="flex justify-between items-center border-b border-slate-700 pb-4">
          <h3 className="font-bold text-lg">Export Beneficiaries by Program</h3>
          <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8 rounded-full p-0">
            <X size={18} />
          </Button>
        </div>

        <div className="py-4">
          {isLoading ? (
            <div className="flex justify-center items-center p-8">
              <div className="loading loading-spinner loading-lg"></div>
            </div>
          ) : error || !data || data.length === 0 ? (
            <div className="text-center py-8">
              <p>No programs found. Please create a program first.</p>
              <Button 
                variant="default"
                className="mt-4"
                onClick={() => {
                  onClose()
                  // Navigate to program creation page
                  window.location.href = '/programs'
                }}
              >
                Create Program
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="table w-full ">
                <thead>
                  <tr className="border-b border-slate-700">
                    <th className="bg-slate-800 text-center">Program Name</th>
                    <th className="bg-slate-800 text-center">Export</th>
                    <th className="bg-slate-800 text-center">Start Date</th>
                    <th className="bg-slate-800 text-center">End Date</th>
                    <th className="bg-slate-800 text-center">Beneficiaries</th>
                  </tr>
                </thead>
                <tbody>
                  {data.map((program) => (
                    <tr key={program._id || program.id} className="border-b text-center border-slate-700 hover:bg-slate-800">
                      <td>{program.name}</td>
                      <td>
                        {program.selectedApplicantsCount > 0 ? (
                          <ExportSelectedApplicantsButton programId={program._id || program.id} />
                        ) : (
                          <Button variant="outline" size="sm" disabled className="h-8  text-accent ">
                            <FileDown size={16} className="mr-1 text-accent" />
                            No data
                          </Button>
                        )}
                      </td>
                      <td>{formatDate(program.startDate)}</td>
                      <td>{formatDate(program.endDate)}</td>
                      <td>
                        <div className="flex items-center justify-center gap-2">
                          <Users size={16} />
                          <span>{program.selectedApplicantsCount || 0}</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Pagination controls using shadcn/ui buttons */}
              <div className="flex items-center justify-center gap-2 mt-4">
                <Button 
                  variant="outline" 
                  size="icon"
                  onClick={handlePreviousPage}
                  disabled={page <= 1}
                  className="h-8 w-8 p-0 bg-card border border-slate-700"
                >
                  <ChevronLeft size={16} />
                </Button>
                <div className="flex flex-row items-center gap-1">
                  <span className="text-sm">Page</span>
                  <div className="join">
                    <input 
                      type="number" 
                      className="input input-bordered input-sm w-10 text-center bg-card border border-slate-700"
                      value={page}
                      min={1}
                      max={totalPages}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        if (!isNaN(value) && value >= 1 && value <= totalPages) {
                          setPage(value);
                        }
                      }}
                    />
                  </div>
                  <span className="text-sm">of {totalPages}</span>
                </div>
                <Button 
                  variant="outline" 
                  size="icon"
                  onClick={handleNextPage}
                  disabled={page >= totalPages}
                  className="h-8 w-8 p-0 bg-card border border-slate-700"
                >
                  <ChevronRight size={16} />
                </Button>
                <select 
                  className="select hidden md:block select-sm bg-card border border-slate-700 ml-2"
                  value={limit}
                  onChange={(e) => {
                    setLimit(Number(e.target.value));
                    setPage(1); // Reset to first page when changing limit
                  }}
                >
                  <option value="5">5 / page</option>
                  <option value="10">10 / page</option>
                  <option value="20">20 / page</option>
                  <option value="50">50 / page</option>
                </select>
              </div>
            </div>
          )}
        </div>

        <div className="modal-action">
          <Button 
            variant="outline" 
            onClick={onClose} 
            className="border border-slate-700 bg-card text-white"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  )
}