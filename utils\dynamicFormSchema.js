// src/validations/dynamicFormSchema.js
import { z } from "zod";

const inputTypes = ["text", "email", "tel", "number", "date", "textarea", "file", "select"];

export const dynamicFormSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  programid: z.string().min(1, "Program selection is required"),
  fields: z
    .array(
      z.object({
        label: z.string().min(1, "Field label is required"),
        inputType: z.enum(inputTypes),
        required: z.boolean(),
        isDefault: z.boolean().optional(),
        options: z.array(z.string()).optional(),
      })
    )
    .min(1, "At least one field is required"),
});