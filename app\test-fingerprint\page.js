"use client"

import { useDeviceFingerprint } from "@/hooks/useDeviceFingerprint"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Loader2, Fingerprint, Monitor, Clock } from "lucide-react"

export default function TestFingerprintPage() {
  const { fingerprint, fullFingerprint, loading, error, isReady } = useDeviceFingerprint()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Generating device fingerprint...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-red-600 flex items-center gap-2">
              <Fingerprint className="h-5 w-5" />
              Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">{error}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-2 flex items-center justify-center gap-2">
              <Fingerprint className="h-8 w-8" />
              Device Fingerprint Test
            </h1>
            <p className="text-gray-600">
              This page shows your device's unique fingerprint used for preventing duplicate applications.
            </p>
          </div>

          <div className="grid gap-6">
            {/* Main Fingerprint Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Fingerprint className="h-5 w-5" />
                  Device Fingerprint
                  <Badge variant={isReady ? "default" : "secondary"}>
                    {isReady ? "Ready" : "Not Ready"}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-100 p-4 rounded-lg font-mono text-sm break-all">
                  {fingerprint || "No fingerprint generated"}
                </div>
                {fullFingerprint?.timestamp && (
                  <div className="mt-2 text-sm text-gray-600 flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    Generated: {new Date(fullFingerprint.timestamp).toLocaleString()}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Components Breakdown */}
            {fullFingerprint?.components && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Monitor className="h-5 w-5" />
                    Fingerprint Components
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Screen Info */}
                    {fullFingerprint.components.screen && (
                      <div>
                        <h4 className="font-semibold mb-2">Screen Information</h4>
                        <div className="bg-gray-50 p-3 rounded text-sm">
                          <pre>{JSON.stringify(fullFingerprint.components.screen, null, 2)}</pre>
                        </div>
                      </div>
                    )}

                    {/* Platform Info */}
                    {fullFingerprint.components.platform && (
                      <div>
                        <h4 className="font-semibold mb-2">Platform Information</h4>
                        <div className="bg-gray-50 p-3 rounded text-sm">
                          <div><strong>Platform:</strong> {fullFingerprint.components.platform.platform}</div>
                          <div><strong>Vendor:</strong> {fullFingerprint.components.platform.vendor}</div>
                          <div><strong>Cookie Enabled:</strong> {fullFingerprint.components.platform.cookieEnabled.toString()}</div>
                        </div>
                      </div>
                    )}

                    {/* Language Info */}
                    {fullFingerprint.components.language && (
                      <div>
                        <h4 className="font-semibold mb-2">Language Information</h4>
                        <div className="bg-gray-50 p-3 rounded text-sm">
                          <div><strong>Language:</strong> {fullFingerprint.components.language.language}</div>
                          <div><strong>Languages:</strong> {fullFingerprint.components.language.languages}</div>
                        </div>
                      </div>
                    )}

                    {/* Timezone Info */}
                    {fullFingerprint.components.timezone && (
                      <div>
                        <h4 className="font-semibold mb-2">Timezone Information</h4>
                        <div className="bg-gray-50 p-3 rounded text-sm">
                          <div><strong>Timezone:</strong> {fullFingerprint.components.timezone.timezone}</div>
                          <div><strong>Offset:</strong> {fullFingerprint.components.timezone.timezoneOffset} minutes</div>
                        </div>
                      </div>
                    )}

                    {/* Hardware Info */}
                    {fullFingerprint.components.hardware && (
                      <div>
                        <h4 className="font-semibold mb-2">Hardware Information</h4>
                        <div className="bg-gray-50 p-3 rounded text-sm">
                          <pre>{JSON.stringify(fullFingerprint.components.hardware, null, 2)}</pre>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Usage Information */}
            <Card>
              <CardHeader>
                <CardTitle>How It Works</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <p>
                    <strong>Device fingerprinting</strong> helps prevent multiple applications from the same device by creating a unique identifier based on your browser and device characteristics.
                  </p>
                  <p>
                    The fingerprint is generated using various factors including:
                  </p>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Screen resolution and color depth</li>
                    <li>Browser platform and vendor information</li>
                    <li>Language and timezone settings</li>
                    <li>Hardware capabilities (CPU cores, memory)</li>
                    <li>Canvas and WebGL rendering signatures</li>
                    <li>Audio context fingerprint</li>
                  </ul>
                  <p className="text-gray-600">
                    <strong>Privacy Note:</strong> This fingerprint is only used to prevent duplicate applications and is not used for tracking or advertising purposes.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
