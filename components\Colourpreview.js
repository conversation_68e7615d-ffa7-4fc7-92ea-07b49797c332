"use client"

export default function ColorExamples() {
  return (
    <div className="space-y-8 p-6">
      <div>
        <h2 className="text-xl font-bold mb-4">Dark Blue Palette</h2>
        <div className="grid grid-cols-11 gap-2">
          {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950].map((shade) => (
            <div key={shade} className="text-center">
              <div className={`bg-dark-blue1-${shade} h-16 rounded-md mb-2`} title={`bg-dark-blue-${shade}`}></div>
              <span className="text-xs">{shade}</span>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h2 className="text-xl font-bold mb-4">Dark Navy Palette</h2>
        <div className="grid grid-cols-11 gap-2">
          {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950].map((shade) => (
            <div key={shade} className="text-center">
              <div className={`bg-dark:DEFAULT navy-${shade} h-16 rounded-md mb-2`} title={`bg-dark-navy-${shade}`}></div>
              <span className="text-xs">{shade}</span>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h2 className="text-xl font-bold mb-4">Dark Background Colors</h2>
        <div className="grid grid-cols-5 gap-4">
          <div className="text-center">
            <div className="bg-dark-bg h-16 rounded-md mb-2" title="bg-dark-bg"></div>
            <span className="text-xs">bg-dark-bg</span>
          </div>
          <div className="text-center">
            <div className="bg-dark-bg-card h-16 rounded-md mb-2" title="bg-dark-bg-card"></div>
            <span className="text-xs">bg-dark-bg-card</span>
          </div>
          <div className="text-center">
            <div className="bg-dark-bg-secondary h-16 rounded-md mb-2" title="bg-dark-bg-secondary"></div>
            <span className="text-xs">bg-dark-bg-secondary</span>
          </div>
          <div className="text-center">
            <div className="bg-dark-bg-accent h-16 rounded-md mb-2" title="bg-dark-bg-accent"></div>
            <span className="text-xs">bg-dark-bg-accent</span>
          </div>
          <div className="text-center">
            <div className="bg-dark-bg-border h-16 rounded-md mb-2" title="bg-dark-bg-border"></div>
            <span className="text-xs">bg-dark-bg-border</span>
          </div>
        </div>
      </div>

      <div>
        <h2 className="text-xl font-bold mb-4">Dark Blue Accent Colors</h2>
        <div className="grid grid-cols-4 gap-4">
          <div className="text-center">
            <div className="bg-dark-blue-accent h-16 rounded-md mb-2" title="bg-dark-blue-accent"></div>
            <span className="text-xs">bg-dark-blue-accent</span>
          </div>
          <div className="text-center">
            <div className="bg-dark-blue-accent-hover h-16 rounded-md mb-2" title="bg-dark-blue-accent-hover"></div>
            <span className="text-xs">bg-dark-blue-accent-hover</span>
          </div>
          <div className="text-center">
            <div className="bg-dark-blue-accent-active h-16 rounded-md mb-2" title="bg-dark-blue-accent-active"></div>
            <span className="text-xs">bg-dark-blue-accent-active</span>
          </div>
          <div className="text-center">
            <div className="bg-dark-blue-accent-focus h-16 rounded-md mb-2" title="bg-dark-blue-accent-focus"></div>
            <span className="text-xs">bg-dark-blue-accent-focus</span>
          </div>
        </div>
      </div>

      <div>
        <h2 className="text-xl font-bold mb-4">Example Usage</h2>
        <div className="space-y-4">
          <div className="bg-dark-bg p-4 rounded-lg">
            <p className="text-dark-blue1-50">This is text on the dark background</p>
          </div>

          <button className="bg-dark-blue-accent hover:bg-dark-blue-accent-hover text-white px-4 py-2 rounded-md">
            Blue Button
          </button>

          <div className="bg-dark-bg-card border border-dark-bg-border p-4 rounded-lg">
            <h3 className="text-dark-blue-50 font-bold">Card Title</h3>
            <p className="text-dark-bg-muted">This is muted text in a card</p>
          </div>

          <div className="bg-dark-bg-secondary p-4 rounded-lg">
            <p className="text-dark-blue-200">Secondary container with light text</p>
          </div>

          <div className="bg-dark1-navy-800 p-4 rounded-lg">
            <p className="text-dark-blue-300">Navy background with blue text</p>
          </div>
        </div>
      </div>
    </div>
  )
}

