import { Badge } from "@/components/ui/badge";
import {
  Users,
  Calendar,
  MapPin,
  Clock,
  Link,
  CheckCircle,
  XCircle,
  FormInput,
  Copy,
  Check,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";

export function ProgramOverview({ program, programForm }) {
  const [copied, setCopied] = useState(false);

  // Calculate remaining days or completion status
  const getStatusDetails = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize to start of day for accurate day calculation
    
    const startDate = new Date(program.startDate);
    startDate.setHours(0, 0, 0, 0);
    
    const endDate = new Date(program.endDate);
    endDate.setHours(0, 0, 0, 0);
  
    if (program.status === "completed") {
      return {
        label: "Completed",
        icon: <CheckCircle className="h-4 w-4 text-blue-500" />,
      };
    } else if (program.status === "upcoming") {
      // Calculate days to start (including edge cases)
      const daysToStart = Math.floor((startDate - today) / (1000 * 60 * 60 * 24));
      
      if (daysToStart === 0) {
        return {
          label: "Starts today",
          icon: <Clock className="h-4 w-4 text-amber-500" />,
        };
      } else if (daysToStart === 1) {
        return {
          label: "Starts tomorrow",
          icon: <Clock className="h-4 w-4 text-amber-500" />,
        };
      } else {
        return {
          label: `Starts in ${daysToStart} days`,
          icon: <Clock className="h-4 w-4 text-amber-500" />,
        };
      }
    } else {
      // Must be ongoing
      const daysToEnd = Math.floor((endDate - today) / (1000 * 60 * 60 * 24));
      
      if (daysToEnd === 0) {
        return {
          label: "Ends today",
          icon: <Clock className="h-4 w-4 text-lime-500" />,
        };
      } else if (daysToEnd === 1) {
        return {
          label: "Ends tomorrow",
          icon: <Clock className="h-4 w-4 text-lime-500" />,
        };
      } else {
        return {
          label: `${daysToEnd} days remaining till end`,
          icon: <Clock className="h-4 w-4 text-lime-500" />,
        };
      }
    }
  };

  const getFormStatusBadge = () => {
    if (!programForm) return null;

    const statusColors = {
      opened: "border-emerald-600 text-emerald-600",
      draft: "border-amber-500 text-amber-500",
      closed: "border-red-500 text-red-500",
      pending: "border-blue-500 text-blue-500",
    };

    const color =
      statusColors[programForm.status] || "border-gray-500 text-gray-500";

    return (
      <Badge
        variant="outline"
        className={`px-2 py-1 text-xs font-bold uppercase ${color}`}
      >
        {programForm.status}
      </Badge>
    );
  };

  const copyToClipboard = () => {
    // Only run this code in the browser, not during server-side rendering
    if (typeof window !== "undefined" && programForm && programForm.formLink) {
      // Use the fallback method which works in more browsers
      const textArea = document.createElement("textarea");
      textArea.value = programForm.formLink;
      textArea.style.position = "fixed"; // Make it invisible
      textArea.style.opacity = "0";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        // Try the execCommand approach
        const successful = document.execCommand("copy");
        if (successful) {
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
        } else {
          console.error("Copy command was unsuccessful");
        }
      } catch (err) {
        console.error("Failed to copy: ", err);
      }

      document.body.removeChild(textArea);
    }
  };

  const statusInfo = getStatusDetails();

  return (
    <div className="bg-card border  border-slate-800 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
      {/* Header */}
      <div className="p-5 border-b border-slate-800">
        <div className="flex justify-between   items-center mb-2">
          <h2 className="text-md max-w-8/12  md:text-xl break-words font-bold text-white">{program.name}</h2>
          <Badge
            variant="outline"
            className={`  px-2 py-1 text-xs font-bold uppercase ${
              program.status === "ongoing"
                ? "border-lime-600 text-lime-600"
                : program.status === "upcoming"
                ? "border-amber-500 text-amber-500"
                : "border-blue-500 text-blue-500"
            }`}
          >
            {program.status}
          </Badge>
        </div>
        <p className="text-gray-400 break-words text-sm line-clamp-3">
          {program.description}
        </p>
      </div>

      {/* Bento Grid Layout */}
      <div className="grid  font-bold grid-cols-1 md:grid-cols-2 gap-1 p-1">
        {/* Date Info */}
        <div className="border border-slate-700 p-4 rounded-lg">
          <div className="flex items-center text-gray-300 mb-2">
            <Calendar className="h-4 w-4 mr-2 text-indigo-400" />
            <span className="text-xs font-medium">DURATION</span>
          </div>
          <div className="text-gray-200 flex text-sm">
            <p>Starts: {new Date(program.startDate).toLocaleDateString()}</p>
            <span className="mx-3">---</span>
            <p>Ends: {new Date(program.endDate).toLocaleDateString()}</p>
          </div>
        </div>

        {/* Status Info */}
        <div className="border border-slate-700 p-4 rounded-lg">
          <div className="flex items-center text-gray-300 mb-2">
            {statusInfo.icon}
            <span className="text-xs font-medium ml-2">COUNTDOWN</span>
          </div>
          <div className="text-gray-200 text-sm">
            <p>{statusInfo.label}</p>
          </div>
        </div>

        {/* Venue */}
        <div className="border border-slate-700 p-4 rounded-lg">
          <div className="flex items-center text-gray-300 mb-2">
            <MapPin className="h-4 w-4 mr-2 text-rose-400" />
            <span className="text-xs font-medium">VENUE</span>
          </div>
          <p className="text-gray-200 text-sm break-words  line-clamp-3">{program.venue}</p>
        </div>

        {/* Selection Process Status */}
        <div className="border border-slate-700 p-4 rounded-lg">
          <div className="flex items-center text-gray-300 mb-2">
            {program.selectionProcess ? (
              <CheckCircle className="h-4 w-4 mr-2 text-emerald-400" />
            ) : (
              <XCircle className="h-4 w-4 mr-2 text-amber-400" />
            )}
            <span className="text-xs font-medium">SELECTION</span>
          </div>
          <p className="text-gray-200 text-sm">
            {program.selectionProcess
              ? "Selection process completed"
              : "Selection process pending"}
          </p>
        </div>

        {/* Capacity and Form Status */}
        <div className="border border-slate-700 p-4 rounded-lg ">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <div>
              <div className="flex items-center text-gray-300 mb-2">
                <Users className="h-4 w-4 mr-2 text-sky-400" />
                <span className="text-xs font-medium">CAPACITY</span>
              </div>
              <p className="text-gray-200 text-sm">
                Max Beneficiaries: {program.maxBeneficiaries}
              </p>
            </div>
          </div>

       </div>   
          <div className="border border-slate-700  p-2 rounded-lg">
          {programForm ? ( // Only render this entire block if programForm is available (truthy)
  <div className="flex flex-col sm:justify-between items-center ">
    {/* Display Form Status - Renders only if programForm exists */}
    <div className="flex items-center text-gray-300 mb-2">
      <Link className="h-4 w-4 mr-2 text-purple-400" /> {/* Ensure Link is imported/defined */}
      <span className="text-xs font-medium">APPLICATION FORM STATUS</span>
      <span className="ml-2">{getFormStatusBadge()}</span>
    </div>

    {/* Check specifically for programForm.formLink */}
    {programForm.formLink ? (
      // If programForm exists AND programForm.formLink has a value (is truthy)
      <Button
        variant="outline"
        size="sm"
        className="text-xs w-full border-slate-700 hover:bg-slate-800 text-accent"
        onClick={copyToClipboard}
      >
        {copied ? (
          <>
            <Check className="h-3 w-3 mr-1 text-green-500" /> {/* Ensure Check is imported/defined */}
            Copied!
          </>
        ) : (
          <>
            <Copy className="h-3 w-3 mr-1" /> {/* Ensure Copy is imported/defined */}
            Copy Form Link
          </>
        )}
      </Button>
    ) : (
      // If programForm exists BUT programForm.formLink does NOT have a value (is falsy)
      <span className="text-xs text-gray-400 italic"> {/* Adjusted text and maybe styling */}
        Form link not available.
      </span>
    )}
  </div>

):(

 
  <span className="text-xs flex items-center  text-gray-400 italic">
    <Link className="w-3 h-3"/>Form link not available.
  </span>
)}



          </div>
       
      </div>
    </div>
  );
}
