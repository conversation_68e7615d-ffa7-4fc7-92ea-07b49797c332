"use client"
import {  useState } from 'react';
import CountUp from 'react-countup';

export default function ProgramStatsCard({ data }) {
 
  
  
  
  return (
    <div className="mb-4 grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="bg-card border sm:hidden border-slate-700 p-4 rounded-lg text-white">
        <h3 className="text-sm text-white font-medium ">Programs</h3>
        <p className="text-2xl font-bold">Summary</p>
      </div>
      
      <div className="bg-card border border-slate-700 p-4 rounded-lg text-white">
        <h3 className="text-sm text-white font-medium">Total Programs</h3>
        <p className="text-2xl font-bold">
          {isClient ? (
            <CountUp end={data?.totalPrograms || 0} duration={2} separator="," />
          ) : (
            data?.totalPrograms || 0
          )}
        </p>
      </div>
      
      <div className="bg-card border border-slate-700 p-4 rounded-lg text-white">
        <h3 className="text-sm font-medium text-white">Filtered Programs</h3>
        <p className="text-2xl font-bold">
          {isClient ? (
            <CountUp end={data?.filteredPrograms || 0} duration={2} separator="," />
          ) : (
            data?.filteredPrograms || 0
          )}
        </p>
      </div>
    </div>
  );
}