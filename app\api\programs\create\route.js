import { NextResponse } from "next/server";
import slugify from "slugify";
import Program from "@/models/Program";
import { dbConnect } from "@/lib/db";
import programSchema from "@/utils/programSchema";
import { rateLimit } from "@/lib/ratelimit";
import { withAdminAuth } from "@/middleware/authMiddleware";
import { sanitizeInput } from "@/lib/sanitize";
import { createLogger } from "@/lib/logger";

// Create logger for this route
const logger = createLogger({ service: "api/programs" });

// Create rate limiter for program creation
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // Maximum program creations allowed
  keyPrefix: "program-create",
});

async function handler(req) {
  // Apply rate limiting
  const { success, response } = await limiter(req);
  if (!success) {
    logger.warn("Rate limit exceeded for program creation", { 
      admin: req.user?.email,
      ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown"
    });
    return response;
  }

  logger.info("Program creation initiated", { 
    admin: req.user.email
  });

  try {
    await dbConnect();
    
    // Get raw body and sanitize input
    const rawBody = await req.json();
    const body = sanitizeInput(rawBody);
    
    // Validate with schema
    const parsedData = programSchema.safeParse(body);

    if (!parsedData.success) {
      logger.warn("Program validation failed", {
        admin: req.user.email,
        errors: parsedData.error.format()
      });
      return NextResponse.json({ error: parsedData.error.format() }, { status: 400 });
    }

    const { name } = parsedData.data;

    // Check if program already exists
    const existingProgram = await Program.findOne({ name: new RegExp(`^${name}$`, "i") });
    if (existingProgram) {
      logger.warn("Duplicate program creation attempt", {
        admin: req.user.email,
        programName: name
      });
      return NextResponse.json({ error: "A program with this name already exists" }, { status: 409 });
    }

    const linkName = slugify(name, { lower: true, strict: true });
    const newProgram = await Program.create({ ...parsedData.data, linkName });

    logger.info("Program created successfully", {
      admin: req.user.email,
      programId: newProgram._id,
      programName: name
    });

    return NextResponse.json({ message: "Program created successfully", id: newProgram._id }, { status: 201 });
  } catch (error) {
    logger.error("Program creation error", {
      admin: req.user?.email,
      error: error.message,
      stack: error.stack
    });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// Export the route handler with admin authentication middleware
export const POST = withAdminAuth(handler);