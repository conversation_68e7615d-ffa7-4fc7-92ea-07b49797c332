"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Home,
  LayoutDashboard,
  FileText,
  Users,
  UserPlus,
  UserCheck,
  User,
} from "lucide-react";

const pathIconMap = {
  dashboard: <LayoutDashboard className="h-4 w-4 mr-1" />,
  programs: <FileText className="h-4 w-4 mr-1" />,
  applicants: <Users className="h-4 w-4 mr-1" />,
  beneficiaries: <UserPlus className="h-4 w-4 mr-1" />,
  manageusers: <UserCheck className="h-4 w-4 mr-1" />,
  profile: <User className="h-4 w-4 mr-1" />,
};

const pathLabelMap = {
  dashboard: "Dashboard",
  programs: "Programs",
  applicants: "Applicants",
  beneficiaries: "Beneficiaries",
  manageusers: "Manage Users",
  profile: "Profile",
  programdetails: "Program Details",
};

function isObjectId(segment) {
  return /^[a-f\d]{24}$/i.test(segment);
}

export default function Breadcrumbs() {
  const pathname = usePathname();
  const pathParts = pathname.split("/").filter(Boolean);

  const breadcrumbs = pathParts
    .map((part, index) => {
      if (isObjectId(part)) return null;

      const href = "/" + pathParts.slice(0, index + 1).join("/");

      return {
        href,
        label: pathLabelMap[part.toLowerCase()] || part.replace(/-/g, " "),
        icon: pathIconMap[part.toLowerCase()] || null,
      };
    })
    .filter(Boolean);

  return (
    <nav className="text-sm mt-4 mb-2 ml-6">
      <ol className="flex items-center flex-wrap space-x-1 text-muted-foreground">
        <li className="flex items-center">
          <Link href="/dashboard" className="flex items-center space-x-1 hover:underline">
            <Home className="h-4 w-4 mr-1" />
            <span>Home</span>
          </Link>
          {breadcrumbs.length > 0 && <span className="mx-2">/</span>}
        </li>
        {breadcrumbs.map((crumb, idx) => (
          <li key={idx} className="flex items-center">
            {idx === breadcrumbs.length - 1 ? (
              <span className="flex items-center font-semibold text-foreground">
                {crumb.icon}
                <span>{crumb.label.charAt(0).toUpperCase() + crumb.label.slice(1)}</span>
              </span>
            ) : (
              <Link
                href={crumb.href}
                className="flex items-center hover:underline"
              >
                {crumb.icon}
                <span>{crumb.label.charAt(0).toUpperCase() + crumb.label.slice(1)}</span>
              </Link>
            )}
            {idx < breadcrumbs.length - 1 && <span className="mx-2">/</span>}
          </li>
        ))}
      </ol>
    </nav>
  );
}
