"use client";
import { TableRow, TableCell } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { useRouter } from "next/navigation";

export default function ApplicantsTableBody({ applicants, getBadgeVariant }) {
  const router = useRouter();
 
  if (!applicants || applicants.length === 0) {
    return (
      <TableRow>
        <TableCell colSpan={5} className="text-center py-4">
          No applicants found.
        </TableCell>
      </TableRow>
    );
  }

  return applicants.map((applicant) => (
    <TableRow key={applicant._id} className="border-b-slate-600">
      <TableCell>
        <div className="font-medium">{applicant.formData.name}</div>
      </TableCell>
      <TableCell>
       
        <div className="text-sm text-white">{applicant.formData.phone}</div>
      </TableCell>
      <TableCell>{applicant.programName}</TableCell>
      <TableCell>
        <Badge className={getBadgeVariant(applicant.status)}>{applicant.status}</Badge>
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="p-2 bg-blue-900 rounded-full hover:bg-blue-700">
              <MoreHorizontal size={20} />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className=" bg-card border text-white border-slate-700">
            <DropdownMenuItem onClick={() => router.push(`/applicants/view/${applicant._id}`)}>
              View Details
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  ));
}
