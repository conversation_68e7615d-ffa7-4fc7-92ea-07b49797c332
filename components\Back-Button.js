"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"

export default function BackButton() {
  // Handle going back to previous page
  const handleBack = () => {
    window.history.back()
  }

  return (
    <Button
      variant="secondary"
      size="icon"
      className="rounded-xl px-15 text-white ml-2 mt-2 bg-card border-2 border-slate-700 hover:bg-blue-900 shadow-md"
      onClick={handleBack}
      aria-label="Go back"
    >
     <ArrowLeft className="h-4 w-4" />
    </Button>
  )
}
