// components/TableSkeleton.jsx
"use client";

export default function TableSkeleton({ columns = 4, rows = 5 }) {
  return (
    <div className="border bg-card rounded-lg">
      <div className="overflow-x-auto">
        <table className="table w-full">
          {/* Table header */}
          <thead>
            <tr>
              {Array(columns)
                .fill(0)
                .map((_, index) => (
                  <th key={`header-${index}`} className="h-6">
                    <div className="skeleton w-24"></div>
                  </th>
                ))}
            </tr>
          </thead>

          {/* Table body */}
          <tbody>
            {Array(rows)
              .fill(0)
              .map((_, rowIndex) => (
                <tr key={`row-${rowIndex}`}>
                  {Array(columns)
                    .fill(0)
                    .map((_, colIndex) => (
                      <td key={`cell-${rowIndex}-${colIndex}`} className="p-4">
                        <div className="flex flex-col gap-2">
                          <div className="skeleton w-full h-4"></div>
                          {/* Conditional content for the first column */}
                          {colIndex === 0 && (
                            <>
                              <div className="skeleton bg-background w-3/4 h-3"></div>
                              <div className="skeleton w-1/2 h-3"></div>
                            </>
                          )}
                        </div>
                      </td>
                    ))}
                </tr>
              ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
