"use client";
import DrawerLayout from "@/components/DrawerLayout";
import { useState, useEffect, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Loader2 } from "lucide-react";
import AuthOnly from "@/components/AuthOnly";
// Custom extracted components
import ApplicantsFilters from "@/components/ApplicantsFilters";
import ApplicantsTableBody from "@/components/ApplicantsTableBody";
import ApplicantsPagination from "@/components/ApplicantsPagination";
import ApplicantStatsCards from "@/components/ApplicantStatsCards";

export default function ApplicantsTable() {
  const [search, setSearch] = useState("");
  const [programId, setProgramId] = useState("all");
  const [status, setStatus] = useState("all");
  const [page, setPage] = useState(1);
  const limit = 10;
  const searchInputRef = useRef(null);

  // Add program pagination state
  const [programPage, setProgramPage] = useState(1);
  const programLimit = 5;

  // Debounce search input
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [isDebouncing, setIsDebouncing] = useState(false);
  
  useEffect(() => {
    setIsDebouncing(true);
    const timeout = setTimeout(() => {
      setDebouncedSearch(search);
      setIsDebouncing(false);
    }, 500);
    return () => clearTimeout(timeout);
  }, [search]);

  // Fetch applicants
  const { data, isLoading, isError, isFetching } = useQuery({
    queryKey: ["applicants", page, debouncedSearch, programId, status],
    queryFn: async () => {
      const params = new URLSearchParams({
        page,
        limit,
        search: debouncedSearch,
        ...(programId !== "all" && { programId }),
        ...(status !== "all" && { status }),
      }).toString();
        
      const response = await axios.get(`/api/applicants/fetch?${params}`);
      
      return response.data;
    },
    keepPreviousData: true,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch programs for filter dropdown with pagination
  const { data: programsData } = useQuery({
    queryKey: ["programs", programPage, programLimit],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: programPage.toString(),
        limit: programLimit.toString(),
        fields: "name,_id",
      }).toString();

      const response = await axios.get(`/api/programs/fetch?${params}`);
     
      return response.data || { programs: [], pagination: { pages: 1, total: 0 } };
    },
    keepPreviousData: true,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Function to handle program pagination
  const handleProgramPageChange = (newPage) => {
    
    setProgramPage(newPage);
  };

  // Debug logging for pagination
  useEffect(() => {
    console.log("Program pagination state:", {
      programPage,
      programTotalPages: programsData?.pagination?.pages || 1,
      programsCount: programsData?.programs?.length || 0
    });
  }, [programPage, programsData]);

  // Reset to page 1 when filters change
  useEffect(() => {
    setPage(1);
  }, [programId, status, debouncedSearch]);

  const resetFilters = () => {
    setSearch("");
    setProgramId("all");
    setStatus("all");
    setPage(1);
  };

  const getBadgeVariant = (status) => {
    switch (status) {
      case "approved":
        return "border-lime-600 w-20  text-lime-600";
      case "pending":
        return "border-amber-500 w-20 text-amber-500";
      case "rejected":
        return "border-rose-600 w-20 text-rose-600";
      case "selected":
        return "border-blue-500 w-20 text-blue-500";
      default:
        return "border-slate-500 w-20 text-slate-500";
    }
  };

  if (isError) return <div className="text-center text-red-500">Failed to fetch applicants.</div>;
  
  // Calculate program pagination values
  const programTotalPages = programsData?.pagination?.pages || 1;

  return (
    <AuthOnly>
    <DrawerLayout>
      <div className="p-4 relative">
        {/* Loading overlay (only shows during fetching, not debouncing) */}
        {isFetching && !isDebouncing && (
          <div className="absolute inset-0 bg-black/20 z-10 flex items-center justify-center">
            <Loader2 className="animate-spin text-white" size={24} />
          </div>
        )}
        
        <div className={`transition-opacity duration-200 ${isDebouncing ? 'opacity-50' : 'opacity-100'}`}>
          
          {/* Stats Cards Component - Always visible */}
          <ApplicantStatsCards 
            totalApplicants={data?.totalApplicants || 0}
            filteredTotal={data?.filteredTotal || 0}
            programTotal={data?.programTotal || 0}
          />
          
          <ApplicantsFilters
            search={search}
            setSearch={setSearch}
            programId={programId}
            setProgramId={setProgramId}
            status={status}
            setStatus={setStatus}
            resetFilters={resetFilters}
            programsData={programsData?.programs || []}
            searchInputRef={searchInputRef}
            programPage={programPage}
            programTotalPages={programTotalPages}
            onProgramPageChange={handleProgramPageChange}
          />

          {/* Table - Always visible with inline skeleton for loading state */}
          <div className="border border-slate-700 bg-card p-4 text-white rounded-lg overflow-x-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Applicants List</h2>
              <p className="text-sm font-mono">
                Showing {data?.applicants?.length || 0} of {data?.filteredTotal || 0} applicants
              </p>
            </div>
            
            <Table>
              <TableHeader className="text-white">
                <TableRow className="border-b-slate-600">
                  <TableHead className="text-white">Name</TableHead>
                  <TableHead className="text-white">Contact</TableHead>
                  <TableHead className="text-white">Program Name</TableHead>
                  <TableHead className="text-white">Status</TableHead>
                  <TableHead className="text-white">Actions</TableHead>
                </TableRow>
              </TableHeader>

              <TableBody>
                {isLoading ? (
                  // Inline table skeleton rows
                  <>
                    {[...Array(5)].map((_, i) => (
                      <TableRow key={i} className="border-b border-slate-700">
                        {[...Array(5)].map((_, j) => (
                          <TableCell key={j}>
                            <div className="h-6 bg-slate-700/50 rounded animate-pulse"></div>
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </>
                ) : (
                  <ApplicantsTableBody applicants={data?.applicants} getBadgeVariant={getBadgeVariant} />
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination - conditionally rendered based on data availability */}
          {data?.totalPages > 1 && (
            <ApplicantsPagination page={page} setPage={setPage} totalPages={data.totalPages} />
          )}
        </div>
      </div>
    </DrawerLayout></AuthOnly>
  );
}