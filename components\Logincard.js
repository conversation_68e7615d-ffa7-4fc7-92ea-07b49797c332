"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { signIn, useSession } from "next-auth/react";
import Link from "next/link";
import { toast } from "sonner";
import { User, Lock, LogIn, HelpCircle, Loader2, LayoutDashboard } from "lucide-react";

export default function LoginCard() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { data: session, status } = useSession();

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);

    const res = await signIn("credentials", {
      username,
      password,
      redirect: false,
    });

    if (res?.error) {
      if (res.error === "disabled") {
        toast.error("Your account has been disabled. Please contact support.");
      } else if (res.error === "rate-limit") {
        toast.error("Too many login attempts. Please try again later.");
      } else if (res.error === "database-error") {
        toast.error("connection error. Please try again later.");
      } else {
        toast.error("Invalid Username or Password");
      }
    } else {
      toast.success("Login successful!");
      setTimeout(() => router.push("/dashboard"), 1000);
    }
    
    setLoading(false);
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const goToDashboard = () => {
    router.push("/dashboard");
  };

  // If already logged in, show "Already logged in" view
  if (status === "authenticated") {
    return (
      <div className="flex w-full -mt-7 md:mt-0 items-center px-4 justify-center">
        <div className="w-full max-w-md p-8 bg-white dark:bg-card shadow-xl rounded-xl border border-gray-200 dark:border-gray-700">
          <div className="flex justify-center mb-6">
            <div className="h-12 w-12 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
              <User className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
          
          <h2 className="text-2xl font-bold text-center mb-4 text-gray-800 dark:text-white">You're Logged In</h2>
          
          <p className="text-center font-mono font-semibold text-gray-600 dark:text-gray-300 mb-6">
            Welcome back, {session.user?.name || session.user?.username || "User"}<br/>! You're currently signed in.
          </p>
          
          <button
            onClick={goToDashboard}
            className="flex items-center border border-blue-600 justify-center w-full py-2 px-4 bg-blue-900 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 text-white font-medium rounded-lg transition-colors"
          >
            <LayoutDashboard className="h-5 w-5 mr-2" />
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  // Show login form if not authenticated or still loading session
  return (
    <div className="flex w-full -mt-7 md:mt-0 items-center px-4 justify-center ">
      <div className="w-full max-w-md p-8 bg-white dark:bg-card shadow-xl rounded-xl border border-gray-200 dark:border-gray-700">
        <div className="flex justify-center mb-6">
          <div className="h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
            <LogIn className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        
        <h2 className="text-2xl font-bold text-center mb-6 text-gray-800 dark:text-white">Welcome Back</h2>
        
        <form onSubmit={handleLogin} className="space-y-5">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Username
            </label>
            <div className="relative flex items-center">
              <User className="absolute left-3 h-5 w-5 text-gray-400" />
              <input
                type="text"
                className="pl-10 pr-4 py-2 w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter your username"
                required
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Password
            </label>
            <div className="relative flex items-center">
              <Lock className="absolute left-3 h-5 w-5 text-gray-400" />
              <input
                type={isPasswordVisible ? "text" : "password"}
                className="pl-10 pr-4 py-2 w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                required
              />
              <button
                type="button"
                className="absolute right-3 text-gray-500 dark:text-gray-400"
                onClick={togglePasswordVisibility}
              >
                {isPasswordVisible ? "Hide" : "Show"}
              </button>
            </div>
          </div>
          
          <div className="pt-2">
            <button
              type="submit"
              className="flex items-center justify-center w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 text-white font-medium rounded-lg transition-colors"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="animate-spin h-5 w-5 mr-2" />
                  Signing In...
                </>
              ) : (
                <>
                  <LogIn className="h-5 w-5 mr-2" />
                  Sign In
                </>
              )}
            </button>
          </div>
        </form>
        
        <div className="mt-6 flex items-center justify-center">
          <Link 
            href="/forgot-password" 
            className="flex items-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            <HelpCircle className="h-4 w-4 mr-1" />
            Forgot password?
          </Link>
        </div>
      </div>
    </div>
  );
}