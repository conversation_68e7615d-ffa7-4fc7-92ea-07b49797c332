"use client";
import { Formik, Form, ErrorMessage, useFormikContext } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import axios from "axios";
import { toast } from "sonner";
import { useState, useRef, useEffect } from "react";

import { useQueryClient } from "@tanstack/react-query";
import programSchema from "@/utils/programSchema";

// Import Shadcn UI Input components
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

// Function to format ISO date to YYYY-MM-DD
const formatDate = (isoDate) => (isoDate ? isoDate.split("T")[0] : "");

// Helper component to reset form when modal closes
const FormResetOnModalClose = ({ isModalOpen, initialValues }) => {
  const { resetForm } = useFormikContext();
  const prevModalOpenRef = useRef(isModalOpen);

  useEffect(() => {
    if (prevModalOpenRef.current && !isModalOpen) {
      // Modal just closed - reset the form to initial values
      resetForm({ values: initialValues });
    }
    prevModalOpenRef.current = isModalOpen;
  }, [isModalOpen, resetForm, initialValues]);

  return null;
};

export default function ProgramEditForm({ program }) {
  const [submitting, setSubmitting] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
 
  const queryClient = useQueryClient();

  // Store initial values separately
  const initialFormValues = {
    name: program.name || "",
    description: program.description || "",
    startDate: formatDate(program.startDate),
    endDate: formatDate(program.endDate),
    venue: program.venue || "",
    maxBeneficiaries: program.maxBeneficiaries || "",
  };

  const handleOpenModal = () => {
    setIsModalOpen(true);
    document.getElementById("edit_program_modal").showModal();
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    document.getElementById("edit_program_modal").close();
  };

  return (
    <>
      {/* Button to open modal */}
      <button className="btn bg-card mr-4 w-60 border hover:bg-blue-900 border-slate-700 rounded-lg" onClick={handleOpenModal}>
        Edit Program Details
      </button>

      {/* DaisyUI Modal */}
      <dialog id="edit_program_modal" className="modal  backdrop-blur-sm bg-opacity-0 modal-middle">
        <div className="modal-box max-h-9/12 bg-card border border-slate-700 text-white">
          <h1 className="text-2xl font-bold text-center mb-4">Edit Program</h1>

          <Formik 
            key={program.id}
            enableReinitialize={true}
            initialValues={initialFormValues}
            validationSchema={toFormikValidationSchema(programSchema)}
            onSubmit={async (values) => {
              try {
                setSubmitting(true);
                const payload = {
                  ...values,
                  linkName: values.name.toLowerCase().replace(/\s+/g, "-"),
                };

                const response = await axios.put(`/api/programs/update/${program._id}`, payload);

                // Invalidate both the summary and specific program details
                queryClient.invalidateQueries(["programsSummary"]);
                queryClient.invalidateQueries(["programDetails", program._id]);

                // Optionally, you can refetch immediately instead of waiting for re-render:
                await queryClient.refetchQueries(["programDetails", program._id]);

                toast.success("✅ Program updated successfully! 🎉");

                handleCloseModal();

              } catch (error) {
                console.error("Error updating program:", error);

                const message =
                  error?.response?.data?.error || "❌ Unexpected error occurred.";

                toast.error(message);
                handleCloseModal();

              } finally {
                setSubmitting(false);
              }
            }}
          >
            {({ isSubmitting, handleChange, handleBlur, values }) => (
              <>
                <FormResetOnModalClose isModalOpen={isModalOpen} initialValues={initialFormValues} />
                <Form className="space-y-4">
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Program Name</span>
                    </label>
                    <Input 
                      name="name"
                      className="bg-card"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.name}
                    />
                    <ErrorMessage name="name" component="p" className="text-red-500 text-sm" />
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Description</span>
                    </label>
                    <Textarea 
                      name="description"
                      className="bg-card"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.description}
                    />
                    <ErrorMessage name="description" component="p" className="text-red-500 text-sm" />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text font-medium">Start Date</span>
                      </label>
                      <Input 
                        type="date"
                        name="startDate"
                        className="bg-card"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.startDate}
                      />
                      <ErrorMessage name="startDate" component="p" className="text-red-500 text-sm" />
                    </div>

                    <div className="form-control">
                      <label className="label">
                        <span className="label-text font-medium">End Date</span>
                      </label>
                      <Input 
                        type="date"
                        name="endDate"
                        className="bg-card"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.endDate}
                      />
                      <ErrorMessage name="endDate" component="p" className="text-red-500 text-sm" />
                    </div>

                    <div className="form-control">
                      <label className="label">
                        <span className="label-text font-medium">Venue</span>
                      </label>
                      <Input 
                        name="venue"
                        className="bg-card"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.venue}
                      />
                      <ErrorMessage name="venue" component="p" className="text-red-500 text-sm" />
                    </div>

                    <div className="form-control">
                      <label className="label">
                        <span className="label-text font-medium">Max Beneficiaries</span>
                      </label>
                      <Input 
                        type="number"
                        name="maxBeneficiaries"
                        className="bg-card"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.maxBeneficiaries}
                      />
                      <ErrorMessage name="maxBeneficiaries" component="p" className="text-red-500 text-sm" />
                    </div>
                  </div>

                  <div className="modal-action">
                    <button 
                      type="button" 
                      className="btn btn-ghost" 
                      onClick={handleCloseModal}
                    >
                      Cancel
                    </button>
                    <button type="submit" className="btn bg-blue-900 border" disabled={isSubmitting}>
                      {isSubmitting ? "Updating..." : "Update Program"}
                    </button>
                  </div>
                </Form>
              </>
            )}
          </Formik>
        </div>
        {/* DaisyUI modal backdrop click handler */}
        <form method="dialog" className="modal-backdrop">
          <button onClick={handleCloseModal}>close</button>
        </form>
      </dialog>
    </>
  );
}