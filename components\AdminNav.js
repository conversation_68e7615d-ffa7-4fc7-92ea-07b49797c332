"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { 
  LayoutDashboard, 
  ClipboardList, 
  Users, 
  BarChart, 
  User, 
  File, 
  UserRoundCheck,
  Package
} from "lucide-react";
import AdminOnly from "./AdminOnly";

export default function AdminNav() {
  const pathname = usePathname();

  const isActive = (href) => {
    return pathname === href || 
           (href !== "/dashboard" && pathname.startsWith(href));
  };

  return (
  
      <ul className="space-y-2 text-white font-bold">
        <li>
          <Link
            href="/dashboard"
            className={`flex items-center gap-2 p-2 rounded-md  hover:bg-slate-800 ${
              isActive("/dashboard") ? "bg-slate-800 border-2 border-slate-700" : ""
            }`}
          >
            <LayoutDashboard size={20} /> Dashboard
          </Link>
        </li>
        <li>
          <Link
            href="/programs"
            className={`flex items-center gap-2 p-2 rounded-md hover:bg-slate-800 ${
              isActive("/programs") ? "bg-slate-800 border-2 border-slate-700" : ""
            }`}
          >
            <ClipboardList size={20} /> Programs
          </Link>
        </li>
        <li>
          <Link
            href="/applicants"
            className={`flex items-center gap-2 p-2 rounded-md  hover:bg-slate-800 ${
              isActive("/applicants") ? "bg-slate-800 border-2 border-slate-700" : ""
            }`}
          >
            <Users size={20} /> Applicants
          </Link>
        </li>
        <li>
          <Link
            href="/beneficiaries"
            className={`flex items-center gap-2 p-2 rounded-md hover:bg-slate-800 ${
              isActive("/beneficiaries") ? "bg-slate-800 border-2 border-slate-700 " : ""
            }`}
          >
            <File size={20} /> Beneficiaries
          </Link>
        </li>
        <AdminOnly>
        <li>
          <Link
            href="/manageusers"
            className={`flex items-center gap-2 p-2 rounded-md hover:bg-slate-800 ${
              isActive("/manageusers") ? "bg-slate-800 border-2 border-slate-700" : ""
            }`}
          >
            <UserRoundCheck size={20} /> Manage Users
          </Link>
        </li>
        </AdminOnly>
        <li>
          <Link
            href="/profile"
            className={`flex items-center gap-2 p-2 rounded-md hover:bg-slate-800 ${
              isActive("/profile") ? "bg-slate-800 border-2 border-slate-700" : ""
            }`}
          >
            <User size={20} /> Profile
          </Link>
        </li>
      </ul>
  
  );
}