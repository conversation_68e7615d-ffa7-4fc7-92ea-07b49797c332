import React from 'react'
import AddUserModal from '@/components/AddUserModal'
import DrawerLayout from '@/components/DrawerLayout'
import UserManagementDashboard from '@/components/ManageUsers'
import AuthOnly from '@/components/AuthOnly'
import AdminOnly from '@/components/AdminOnly'
function page() {
  return (

    <AuthOnly>
      <AdminOnly>
    <DrawerLayout>
        
    <div>
        <div className="p-5 min-h-screen">
            <div className="max-w-md mx-auto p-6 bg-card text-white dark:text-white shadow-lg rounded-lg">
            <h2 className="text-lg font-bold text-center mb-4">Manage Users</h2>
            <AddUserModal />
            {/* Add other components or user lists here */}
            </div>
            <UserManagementDashboard />
        </div>

    </div>
    
    {/* Add other components or user lists here */}
    </DrawerLayout></AdminOnly></AuthOnly>
  )
}

export default page