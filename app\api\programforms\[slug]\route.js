import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import Program from "@/models/Program";
import ProgramForm from "@/models/ProgramForm";

export async function GET(req, { params }) {
  try {
    await dbConnect();

    const { slug } = await params;
    if (!slug) {
      return NextResponse.json({ error: "Slug is required" }, { status: 400 });
    }

    // Find the program using the slug (linkName)
    const program = await Program.findOne({ linkName: slug });

    if (!program) {
      return NextResponse.json({ error: "Program not found" }, { status: 404 });
    }

    // Fetch the form linked to this program
    const form = await ProgramForm.findOne({ programid: program._id });

    if (!form) {
      return NextResponse.json({ error: "Form not found" }, { status: 404 });
    }

    return NextResponse.json({ form }, { status: 200 });
  } catch (error) {
    console.error("Error fetching form:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
