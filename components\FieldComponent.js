import { Trash2, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export default function FieldComponent({
  field,
  index,
  fields,
  setFields,
  removeField,
  inputTypes = [],
  validateLabel,
}) {
  const checkDuplicateLabel = (label, currentIndex) => {
    return fields.some(
      (field, idx) => field.label.trim() === label.trim() && idx !== currentIndex
    );
  };

  const handleLabelChange = (e) => {
    const newLabel = e.target.value;
    const isDuplicate = checkDuplicateLabel(newLabel, index);

    const updatedFields = [...fields];
    updatedFields[index].label = newLabel;

    if (isDuplicate) {
      updatedFields[index].error = "This label is already in use";
    } else {
      delete updatedFields[index].error;
    }

    setFields(updatedFields);
  };

  const removeOption = (optIndex) => {
    const updatedFields = [...fields];
    updatedFields[index].options.splice(optIndex, 1);
    setFields(updatedFields);
  };

  return (
    <div className="bg-card border-dashed p-4 rounded-lg border-2 border-slate-700 relative">
      {/* Remove Field Button */}
      {!field.isDefault && (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => removeField(index)}
          className="absolute top-2 right-2 text-red-500 hover:text-red-700"
        >
          <Trash2 size={18} />
        </Button>
      )}

      {/* Label Input */}
      <div className="space-y-2 mb-4">
        <Label htmlFor={`label-${index}`}>Label</Label>
        <Input
          id={`label-${index}`}
          type="text"
          className={field.error ? "border-red-500" : ""}
          value={field.label}
          disabled={field.isDefault}
          onChange={handleLabelChange}
        />
        {field.error && (
          <p className="text-red-500 text-xs mt-1">{field.error}</p>
        )}
      </div>

      {/* Input Type Select - Now using Daisy UI */}
      {field.inputType !== "select" && (
        <div className="form-control mb-4">
          <label className="label">Input Type</label>
          <select
            className="select select-bordered disabled:bg-background disabled:text-accent bg-background text-accent w-full"
            value={field.inputType}
            disabled={field.isDefault}
            onChange={(e) => {
              const updatedFields = [...fields];
              updatedFields[index].inputType = e.target.value;
              setFields(updatedFields);
            }}
          >
            {inputTypes.map((type) => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Option Manager for Select Fields */}
      {field.inputType === "select" && (
        <div className="form-control mb-4">
          <label className="label">{field.label} Options</label>
          
          {/* For Default Fields: Show as dropdown (using Daisy UI) */}
          {field.isDefault && field.options?.length > 0 && (
            <select className="select select-bordered bg-background text-accent w-full mb-2">
              <option value="" disabled>Select an option</option>
              {field.options.map((option, optIndex) => (
                <option key={optIndex} value={option}>
                  {option}
                </option>
              ))}
            </select>
          )}

          {/* For Custom Fields: Show as tags */}
          {!field.isDefault && field.options?.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-2">
              {field.options.map((option, optIndex) => (
                <Badge key={optIndex} variant="secondary" className="flex items-center gap-1">
                  <span>{option}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 text-red-500 hover:text-red-700 hover:bg-transparent"
                    onClick={() => removeOption(optIndex)}
                  >
                    <X size={14} />
                  </Button>
                </Badge>
              ))}
            </div>
          )}

          {/* Add Option - Only for non-default fields */}
          {!field.isDefault && (
            <div className="flex gap-2">
              <Input
                type="text"
                placeholder="Add option"
                value={field.newOption || ""}
                onChange={(e) => {
                  const updatedFields = [...fields];
                  updatedFields[index].newOption = e.target.value;
                  setFields(updatedFields);
                }}
              />
              <Button
                type="button"
                onClick={() => {
                  const updatedFields = [...fields];
                  const newOption = updatedFields[index].newOption?.trim();
                  if (
                    newOption &&
                    !updatedFields[index].options.includes(newOption)
                  ) {
                    updatedFields[index].options.push(newOption);
                  }
                  updatedFields[index].newOption = "";
                  setFields(updatedFields);
                }}
              >
                Add
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Required Checkbox - Keeping original implementation */}
      <div className="flex items-center gap-2 mt-2">
        <input
          type="checkbox"
          checked={field.required}
          disabled={field.isDefault}
          onChange={(e) => {
            const updatedFields = [...fields];
            updatedFields[index].required = e.target.checked;
            setFields(updatedFields);
          }}
          className="checkbox bg-slate-900 border-2 border-slate-700 "
        />
        <span>Required</span>
      </div>
    </div>
  );
}