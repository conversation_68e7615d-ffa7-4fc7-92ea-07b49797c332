import { Badge } from "@/components/ui/badge";
import {
  Users,
  Calendar,
  MapPin,
  Clock,
  Link,
  CheckCircle,
  FormInput,
} from "lucide-react";
import  AdminOnly  from "@/components/AdminOnly";
export function ProgramOverviewSkeleton() {
  return (
    <div className="p-4 md:p-6 w-full min-h-screen space-y-6">
      <div className="flex overflow-x-auto overflow-y-hidden scroll-hidden">
        <AdminOnly>
          <div className="h-10 bg-slate-700 rounded w-48 mr-4 animate-pulse"></div>
          <div className="h-10 bg-slate-700 rounded w-48 mr-4 animate-pulse"></div>
          <div className="h-10 bg-slate-700 rounded w-48 mr-4 animate-pulse"></div>
          <div className="h-10 bg-slate-700 rounded w-48 mr-4 animate-pulse"></div>
        </AdminOnly>
        <div className="h-10 bg-slate-700 rounded w-48 animate-pulse"></div>

        <style jsx>{`
          .scroll-hidden {
            scrollbar-width: none; 
            -ms-overflow-style: none; 
          }
          .scroll-hidden::-webkit-scrollbar {
            display: none;
          }
        `}</style>
      </div>

      <div className="bg-card border border-slate-800 rounded-2xl overflow-hidden shadow-lg animate-pulse">
        {/* Header */}
        <div className="p-5 border-b border-slate-800">
          <div className="flex justify-between items-center mb-2">
            <div className="h-6 bg-slate-700 rounded w-8/12"></div>
            <div className="h-5 bg-slate-700 rounded w-20"></div>
          </div>
          <div className="h-12 mt-2 bg-slate-700 rounded w-full"></div>
        </div>

        {/* Bento Grid Layout */}
        <div className="grid font-bold grid-cols-1 md:grid-cols-2 gap-1 p-1">
          {/* Date Info */}
          <div className="border border-slate-700 p-4 rounded-lg">
            <div className="flex items-center text-gray-300 mb-2">
              <Calendar className="h-4 w-4 mr-2 text-indigo-400 opacity-50" />
              <span className="text-xs font-medium">DURATION</span>
            </div>
            <div className="flex text-sm">
              <div className="h-4 bg-slate-700 rounded w-24"></div>
              <span className="mx-3">---</span>
              <div className="h-4 bg-slate-700 rounded w-24"></div>
            </div>
          </div>

          {/* Status Info */}
          <div className="border border-slate-700 p-4 rounded-lg">
            <div className="flex items-center text-gray-300 mb-2">
              <Clock className="h-4 w-4 mr-2 text-amber-500 opacity-50" />
              <span className="text-xs font-medium ml-2">COUNTDOWN</span>
            </div>
            <div className="h-4 bg-slate-700 rounded w-36"></div>
          </div>

          {/* Venue */}
          <div className="border border-slate-700 p-4 rounded-lg">
            <div className="flex items-center text-gray-300 mb-2">
              <MapPin className="h-4 w-4 mr-2 text-rose-400 opacity-50" />
              <span className="text-xs font-medium">VENUE</span>
            </div>
            <div className="h-4 bg-slate-700 rounded w-full"></div>
          </div>

          {/* Selection Process Status */}
          <div className="border border-slate-700 p-4 rounded-lg">
            <div className="flex items-center text-gray-300 mb-2">
              <CheckCircle className="h-4 w-4 mr-2 text-emerald-400 opacity-50" />
              <span className="text-xs font-medium">SELECTION</span>
            </div>
            <div className="h-4 bg-slate-700 rounded w-48"></div>
          </div>

          {/* Capacity */}
          <div className="border border-slate-700 p-4 rounded-lg">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
              <div>
                <div className="flex items-center text-gray-300 mb-2">
                  <Users className="h-4 w-4 mr-2 text-sky-400 opacity-50" />
                  <span className="text-xs font-medium">CAPACITY</span>
                </div>
                <div className="h-4 bg-slate-700 rounded w-40"></div>
              </div>
            </div>
          </div>

          {/* Form Status */}
          <div className="border border-slate-700 p-2 rounded-lg">
            <div className="flex flex-col sm:justify-between items-center">
              <div className="flex items-center text-gray-300 mb-2">
                <Link className="h-4 w-4 mr-2 text-purple-400 opacity-50" />
                <span className="text-xs font-medium">APPLICATION FORM STATUS</span>
                <div className="ml-2 h-4 bg-slate-700 rounded w-16"></div>
              </div>
              <div className="h-8 bg-slate-700 rounded w-full"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}