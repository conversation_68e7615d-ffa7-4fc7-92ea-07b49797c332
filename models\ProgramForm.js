const mongoose = require("mongoose");

const ProgramFormSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Form title is required"],
    },
    description: {
      type: String,
    },
    programid: {
      type: String,
      required: [true, "Program ID is required"],
    },
    status: {
      type: String,
      enum: ["opened", "closed"],
      default: "opened",
    },
    formPhase: {
      type: Number,
      default: 1, // Starts at 1 and increments when reopened
    },
    formLink: {
      type: String,
      unique: true, // Ensure uniqueness
    },
    fields: [
      {
        label: {
          type: String,
          required: [true, "Field label is required"],
        },
        inputType: {
          type: String,
          enum: [
            "text",
            "email",
            "tel",
            "number",
            "date",
            "select",
            "textarea",
            "file",
          ],
          required: [true, "Field input type is required"],
        },
        required: {
          type: Boolean,
          default: false,
        },
        options: [
          {
            type: String,
          },
        ],
      },
    ],
  },
  { timestamps: true }
);

export default mongoose.models.ProgramForm || mongoose.model("ProgramForm", ProgramFormSchema);
