import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import Applicant from "@/models/Applicant";
import { withAuth } from "@/middleware/authMiddleware";
import { rateLimit } from "@/lib/ratelimit";
import { createLogger } from "@/lib/logger";

const logger = createLogger({ service: "approve-applicant" });

const limiter = rateLimit({
  windowMs: 60 * 1000,
  max: 30,
  keyPrefix: "patchApplicant"
});

async function handler(req) {
  const rateCheck = await limiter(req);
  if (!rateCheck.success) return rateCheck.response;

  try {
    await dbConnect();

    const { applicantId, reviewerId } = await req.json();

    if (!applicantId) {
      logger.warn("Missing applicantId");
      return NextResponse.json({ error: "Applicant ID is required" }, { status: 400 });
    }

    if (!reviewerId) {
      logger.warn("Missing reviewerId");
      return NextResponse.json({ error: "Reviewer ID is required" }, { status: 400 });
    }

    const applicant = await Applicant.findById(applicantId);

    if (!applicant) {
      logger.warn("Applicant not found", { applicantId });
      return NextResponse.json({ error: "Applicant not found" }, { status: 404 });
    }

    // Prevent approval if status is 'selected'
    if (applicant.status === "selected") {
      logger.warn("Cannot approve applicant with status 'selected'", { applicantId });
      return NextResponse.json({ error: "Cannot approve applicant who is already selected" }, { status: 400 });
    }

    // If already approved by someone else, deny re-approval
    if (applicant.status === "approved") {
      if (applicant.reviewerId?.toString() !== reviewerId) {
        logger.warn("Applicant already approved by a different reviewer", { applicantId, reviewerId });
        return NextResponse.json({ error: "You are not authorized to re-approve this applicant." }, { status: 403 });
      }
    }

    // If reviewed (approved/rejected) by another reviewer, disallow changes
    if (applicant.reviewerId && applicant.reviewerId.toString() !== reviewerId) {
      logger.warn("Applicant already reviewed by another reviewer", { applicantId, reviewerId });
      return NextResponse.json({ error: "Applicant already reviewed by a different reviewer." }, { status: 403 });
    }

    applicant.status = "approved";
    applicant.reviewerId = reviewerId;
    await applicant.save();

    logger.info("Applicant approved", {
      applicantId,
      reviewerId
    });

    return NextResponse.json({
      message: "Applicant approved successfully",
      applicant
    });
  } catch (error) {
    logger.error("Error approving applicant", {
      error: error.message,
      stack: error.stack
    });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export const PATCH = withAuth(handler);
