import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { dbConnect } from "@/lib/db";
import User from "@/models/User";
import bcrypt from "bcryptjs";
import { createLogger } from "@/lib/logger";
import { rateLimit } from "@/lib/ratelimit";

// Create a logger instance for this route
const logger = createLogger({ service: "password-change-api" });

// Apply stricter rate limiting for password change attempts
// This helps prevent brute force attacks
const applyRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minute window
  max: 5, // 5 attempts per 5 minutes
  keyPrefix: "password-change" // Unique prefix for this endpoint
});

export async function POST(req) {
  try {
    // Apply rate limiting first
    const rateLimitResult = await applyRateLimit(req);
    if (!rateLimitResult.success) {
      logger.warn("Rate limit exceeded for password change", {
        ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown",
        path: req.nextUrl.pathname
      });
      return rateLimitResult.response;
    }

    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.email) {
      logger.warn("Unauthorized password change attempt", {
        ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown",
        path: req.nextUrl.pathname
      });
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    logger.info("Password change initiated", {
      user: session.user.email,
      path: req.nextUrl.pathname
    });

    await dbConnect();
    const { oldPassword, newPassword } = await req.json();

    if (!oldPassword || !newPassword) {
      logger.warn("Invalid password change request - missing required fields", {
        user: session.user.email,
        hasOldPassword: !!oldPassword,
        hasNewPassword: !!newPassword
      });
      return NextResponse.json({ error: "All fields are required" }, { status: 400 });
    }

    // Check password strength
    if (newPassword.length < 8) {
      logger.warn("Weak password attempt", {
        user: session.user.email,
        reason: "Password too short"
      });
      return NextResponse.json({ error: "New password must be at least 8 characters long" }, { status: 400 });
    }

    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      logger.error("User not found in database despite valid session", {
        email: session.user.email
      });
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const isMatch = await bcrypt.compare(oldPassword, user.password);
    if (!isMatch) {
      logger.warn("Incorrect old password", {
        user: session.user.email
      });
      return NextResponse.json({ error: "Incorrect old password" }, { status: 400 });
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    user.password = hashedPassword;
    await user.save();

    logger.info("Password changed successfully", {
      user: session.user.email
    });

    return NextResponse.json({ message: "Password updated successfully" });
  } catch (error) {
    logger.error("Change password error", {
      error: error.message,
      stack: error.stack,
      path: req.nextUrl?.pathname || "unknown"
    });
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}