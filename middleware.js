import { NextResponse } from 'next/server';

export function middleware(request) {
  const response = NextResponse.next();
  
  // Add caching headers for static assets
  if (
    request.nextUrl.pathname.startsWith('/images/') || 
    request.nextUrl.pathname.startsWith('/assets/') ||
    request.nextUrl.pathname.endsWith('.jpg') ||
    request.nextUrl.pathname.endsWith('.png') ||
    request.nextUrl.pathname.endsWith('.svg') ||
    request.nextUrl.pathname.endsWith('.css') ||
    request.nextUrl.pathname.endsWith('.js')
  ) {
    // Cache static assets for 1 year
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
  } else if (!request.nextUrl.pathname.startsWith('/api/')) {
    // Cache HTML pages for 5 minutes
    response.headers.set('Cache-Control', 'public, max-age=300, s-maxage=300');
  }
  
  return response;
}

export const config = {
  matcher: [
    '/((?!api/auth|_next/static|_next/image|favicon.ico).*)',
  ],
};