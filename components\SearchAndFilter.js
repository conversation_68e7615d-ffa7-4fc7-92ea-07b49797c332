// components/SearchAndFilter.tsx
import { Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"

export function SearchAndFilter({
  search,
  setSearch,
  status,
  setStatus,
  searchInputRef
}) {
  return (
    <div className="flex justify-between flex-col md:flex-row gap-4 mb-4">
      <div className="relative w-full md:w-1/2">
        <Search className="absolute left-3 top-2 text-accent" size={20} />
        <Input
          ref={searchInputRef}
          type="text"
          placeholder="Search by Name"
          className="w-full pl-9 pr-4 border dark:bg-card border-slate-700"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          autoFocus
        />
      </div>
      <div className="w-full md:w-1/2">
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger className="border w-full border-slate-700 dark:bg-card">
            <SelectValue placeholder="All Statuses" />
          </SelectTrigger>
          <SelectContent className="bg-card border border-slate-700 text-white">
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="upcoming">Upcoming</SelectItem>
            <SelectItem value="ongoing">Ongoing</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}