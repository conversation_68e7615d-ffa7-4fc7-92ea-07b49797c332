"use client";
import BackButton from "./Back-Button";
import { useSession } from "next-auth/react";
import { Menu } from "lucide-react";
import AdminNav from "./AdminNav";
import Breadcrumbs from "./Breadcrumbs";
import { ThemeToggle } from "./theme-toggle"; 
import SignOutButton from "./SignOutButton";
import { User } from "lucide-react";
import Footer from "./Footer";
export default function DrawerLayout({ children }) {
  const { data: session } = useSession();

  return (
    <div className="drawer lg:drawer-open">
      <input id="my-drawer-2" type="checkbox" className="drawer-toggle" />

      {/* Page Content */}
      <div className="drawer-content flex flex-col w-full ">
        <header className="w-full px-4   border-2 border-background items-center  transition-colors bg-card bg-opacity-60">
          <div className="px-4 mx-auto sm:px-6 lg:px-8 pt-4">
            <div className="flex items-center justify-between h-16 lg:h-20">
              {/* Logo + Text */}
              <div className="flex -mt-4 items-center gap-3">
               
                <h1 className="font-monospace text-sm w-2/3 md:w-full font-extrabold xl:text-xl text-white">
                 Haruna Maiwada
                 Communiy Foundation
                </h1>
              </div>
            
                 
              {/* Drawer Button */}
              <label htmlFor="my-drawer-2" className=" -mr-3 p-0.5  rounded-md  border-2 border-gray-500 dark:border-ring -mt-4 drawer-button bg-gray-700  dark:bg-background justify-end lg:hidden flex items-center ">
            <div className="w-11 h-11 flex items-center justify-center">
               <Menu className="w-6 h-6 text-white dark:text-white" />
            </div>
              </label>
            </div>
          </div>
        </header>
        <Breadcrumbs /> 
        {children}
        <Footer />
      </div>

      {/* Sidebar */}
      <div className="drawer-side">
        <label htmlFor="my-drawer-2" aria-label="close sidebar" className="drawer-overlay"></label>
  
        <div className="min-h-full w-64 p-4 space-y-2   flex flex-col justify-between  bg-primary md:bg-card transition-colors h-full">
          {/* User Info */}
              <div>
             <div className="flex items-center mt-2 justify-between">
              <div>
              <h2 className="font-bold text-white"> Menu</h2>

             </div>
             <div> <ThemeToggle/> </div>
              </div>
              <div className="divider  mt-2 mb-2"/>
          {session?.user && (
            <div className="p-3 text-center text-accent mb-2 rounded-lg bg-chart-1  md:bg-background ">
              <p className="text-base font-medium">{session.user.name}</p>
              <p className="text-xs bg- flex items-center justify-center capitalize font-bold ">
                < User className=" " size={15} />
                {session.user.role}
              </p>
            </div>
          )}
          
          {/* Navigation */}
         
            
             <AdminNav />
           </div>
          
          {/* Sign Out Button */}
          <div className=" mb-10">
             <div className="divider"/>
            <SignOutButton />
          </div>
          
        </div>
      </div>
    </div>
  );
}
