import mongoose from "mongoose";

const ApplicantSchema = new mongoose.Schema(
  {
    programId: {
      type: String, // Stores the ID of the related charity program
      required: true,
    },
    formPhase: {
      type: Number, // Tracks the phase in which the applicant applied
      required: true, // Must be explicitly set based on the current ProgramForm phase
    },
    formData: {
      type: Map,
      of: mongoose.Schema.Types.Mixed, // Allows dynamic form fieldsa
      required: true,
    },
    status: {
      type: String,
      enum: ["pending", "approved", "rejected", "selected"], // Status tracking
      default: "pending",
    },
    reviewerId: {
      type: String,
      default: null, // Default value is null
    },
    deviceFingerprint: {
      type: String,
      required: false, // Optional for backward compatibility
      index: true, // Add index for faster queries
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  { timestamps: true }
);

// Create compound unique index to prevent duplicate phone numbers per program
ApplicantSchema.index(
  {
    programId: 1,
    "formData.phonenumber": 1
  },
  {
    unique: true,
    name: "unique_phone_per_program",
    partialFilterExpression: {
      "formData.phonenumber": { $exists: true, $ne: null, $ne: "" }
    }
  }
);

// Create compound unique index to prevent duplicate device fingerprints per program
ApplicantSchema.index(
  {
    programId: 1,
    deviceFingerprint: 1
  },
  {
    unique: true,
    name: "unique_device_per_program",
    partialFilterExpression: {
      deviceFingerprint: { $exists: true }
    }
  }
);

export default mongoose.models.Applicant || mongoose.model("Applicant", ApplicantSchema);
