import { NextResponse } from "next/server";
import mongoose from "mongoose";
import Program from "@/models/Program";
import {dbConnect} from "@/lib/db";

export async function GET(req, { params }) {
  try {
    await dbConnect(); // Connect to MongoDB

    const { id } = await params; // Get program ID from route parameter
    const { searchParams } = new URL(req.url);
    const fields = searchParams.get("fields"); // Get requested fields (optional)

    if (!id) {
      return NextResponse.json({ error: "Program ID is required" }, { status: 400 });
    }

    // Ensure ID is a valid MongoDB ObjectId to prevent injection attacks
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: "Invalid Program ID" }, { status: 400 });
    }

    // Process fields selection
    let selectedFields = null;
    if (fields) {
      const allowedFields = ["name", "description", "startDate", "endDate", "status", "maxBeneficiaries", "formId", "_id", "createdAt", "updatedAt"];
      const requestedFields = fields.split(",").map(field => field.trim()).filter(field => allowedFields.includes(field));
      
      if (requestedFields.length > 0) {
        selectedFields = requestedFields.join(" "); // Convert array to space-separated string for Mongoose
      }
    }

    // Query the program and return only requested fields
    const program = await Program.findById(id).select(selectedFields).lean();

    if (!program) {
      return NextResponse.json({ error: "Program not found" }, { status: 404 });
    }

    return NextResponse.json({ program }, { status: 200 });
  } catch (error) {
    console.error("Error fetching program:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
