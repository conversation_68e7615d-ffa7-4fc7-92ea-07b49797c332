// components/profile/LoadingState.jsx
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { UserIcon, KeyIcon, BookOpenIcon } from "lucide-react"

export default function LoadingState() {
  return (
    <div className="min-h-screen p-4 text-white md:px-7">
      <div className="container">
        {/* Skeleton Tabs */}
        <div className="w-full">
          <div className="flex justify-center  bg-card w-full max-w-md py-2 mb-4 mx-auto rounded-md">
            <div className="flex gap-4">
              <div className="flex items-center text-base py-4 px-4 rounded-md bg-slate-700/50 animate-pulse">
                <UserIcon className="mr-2 h-4 w-4 opacity-50" />
                <div className="w-16 h-5 bg-slate-600 rounded animate-pulse"></div>
              </div>
              <div className="flex items-center text-base py-4 px-4 rounded-md bg-slate-700/20 animate-pulse">
                <KeyIcon className="mr-2 h-4 w-4 opacity-50" />
                <div className="w-16 h-5 bg-slate-600 rounded animate-pulse"></div>
              </div>
            </div>
          </div>

          {/* Skeleton Content */}
          <div className="mt-2 space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Personal Info Card Skeleton */}
              <div className="bg-card rounded-xl shadow-sm p-6 border animate-pulse">
                <h2 className="flex items-center mb-4">
                  <UserIcon className="mr-2 h-5 w-5 opacity-50" />
                  <div className="w-48 h-7 bg-slate-600 rounded"></div>
                </h2>
                <div className="space-y-4">
                  <div>
                    <div className="w-24 h-5 bg-slate-600 rounded mb-1"></div>
                    <div className="w-full h-10 bg-slate-600 rounded"></div>
                    <div className="w-40 h-4 bg-slate-600/50 rounded mt-1"></div>
                  </div>
                  <div>
                    <div className="w-24 h-5 bg-slate-600 rounded mb-1"></div>
                    <div className="w-full h-10 bg-slate-600 rounded"></div>
                  </div>
                  <div>
                    <div className="w-32 h-5 bg-slate-600 rounded mb-1"></div>
                    <div className="w-full h-10 bg-slate-600 rounded"></div>
                    <div className="w-56 h-4 bg-slate-600/50 rounded mt-1"></div>
                  </div>
                </div>
              </div>

              {/* About You Card Skeleton */}
              <div className="bg-card rounded-xl shadow-sm p-6 border flex flex-col animate-pulse">
                <h2 className="flex items-center mb-4">
                  <BookOpenIcon className="mr-2 h-5 w-5 opacity-50" />
                  <div className="w-36 h-7 bg-slate-600 rounded"></div>
                </h2>
                <div className="space-y-4 flex-1">
                  <div>
                    <div className="w-24 h-5 bg-slate-600 rounded mb-1"></div>
                    <div className="w-full h-10 bg-slate-600 rounded"></div>
                    <div className="w-48 h-4 bg-slate-600/50 rounded mt-1"></div>
                  </div>
                  <div className="flex-1">
                    <div className="w-16 h-5 bg-slate-600 rounded mb-1"></div>
                    <div className="w-full h-32 bg-slate-600 rounded"></div>
                    <div className="w-40 h-4 bg-slate-600/50 rounded mt-1"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Save Button Skeleton */}
            <div className="mt-4 flex justify-center">
              <div className="w-full md:w-1/3 h-10 bg-slate-700 rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}