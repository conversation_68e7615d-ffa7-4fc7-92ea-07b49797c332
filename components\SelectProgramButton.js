import { useState } from 'react';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';

function SelectProgramButton({ programId }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const queryClient = useQueryClient();

  // Simple random selection handler
  const handleRandomSelection = async () => {
    setIsLoading(true);
    try {
      const response = await axios.post(`/api/selection/random/${programId}`);

      if (response.status === 200) {
        toast.success(response.data.message || 'Selection successful');

        // Invalidate and refetch queries
        await queryClient.invalidateQueries({
          queryKey: ['programDetails', programId],
        });
        await queryClient.invalidateQueries({
          queryKey: ["ward-stats"],
        });
        await queryClient.invalidateQueries({
          queryKey: ['beneficiaries'],
        });
        await queryClient.refetchQueries({
          queryKey: ['programDetails', programId],
        });
        await queryClient.refetchQueries({
          queryKey: ["programApplicants", programId],
        });

        setIsModalOpen(false);
      } else {
        toast.error(`${response.data.error || 'Unexpected error occurred'}`);
      }
    } catch (error) {
      if (error.response) {
        toast.error(`Error: ${error.response.data.error || 'Unknown error'}`);
      } else {
        toast.error('Network Error: Failed to select applicants');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <button
        className="btn bg-card border rounded-lg w-60 hover:bg-blue-900 border-slate-700"
        onClick={() => setIsModalOpen(true)}
      >
        Initiate Selection Process
      </button>

      {isModalOpen && (
        <dialog className="modal modal-open">
          <div className="modal-box bg-card border border-slate-700 text-white max-w-2xl">
            <button
              className="btn btn-sm btn-circle absolute top-2 right-2 text-gray-400 hover:text-white"
              onClick={() => setIsModalOpen(false)}
            >
              &times;
            </button>

            <h2 className="text-xl text-center mb-4 font-bold">Applicant Selection</h2>

            <div className="space-y-4">
              <p className="text-center">Choose your selection method:</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Random Selection */}
                <div className="border border-slate-600 rounded-lg p-4">
                  <h3 className="font-bold mb-2">Random Selection</h3>
                  <p className="text-sm text-gray-300 mb-4">
                    Equal distribution across all wards with random selection within each ward.
                  </p>
                  <button
                    className="btn bg-blue-900 hover:bg-blue-700 text-white w-full"
                    onClick={handleRandomSelection}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Processing...' : 'Use Random Selection'}
                  </button>
                </div>

                {/* Ward Percentage Selection */}
                <div className="border border-slate-600 rounded-lg p-4">
                  <h3 className="font-bold mb-2">Ward-Based Percentage</h3>
                  <p className="text-sm text-gray-300 mb-4">
                    Set custom percentages for each ward based on applicant distribution.
                  </p>
                  <button
                    className="btn bg-green-700 hover:bg-green-600 text-white w-full"
                    onClick={() => {
                      setIsModalOpen(false);
                      router.push(`/programs/ward-selection/${programId}`);
                    }}
                  >
                    Configure Ward Percentages
                  </button>
                </div>
              </div>

              <p className="text-sm text-center font-bold text-red-400 mt-4">
                ⚠️ Once selection is done, it cannot be reversed!
              </p>
            </div>
          </div>
        </dialog>
      )}
    </div>
  );
}

export default SelectProgramButton;
