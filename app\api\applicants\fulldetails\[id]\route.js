import { NextResponse } from "next/server";
import {dbConnect} from "@/lib/db";
import Applicant from "@/models/Applicant";
import Program from "@/models/Program";
import User from "@/models/User"; // Assuming this stores reviewers (admins)

export async function GET(request, { params }) {
  await dbConnect();

  const { id } = await params;

  if (!id) {
    return NextResponse.json({ error: "Applicant ID is required" }, { status: 400 });
  }

  try {
    const applicant = await Applicant.findById(id).lean();

    if (!applicant) {
      return NextResponse.json({ error: "Applicant not found" }, { status: 404 });
    }

    const [program, reviewer] = await Promise.all([
      Program.findById(applicant.programId).select("name").lean(),
      applicant.reviewerId ? User.findById(applicant.reviewerId).select("name email username").lean() : null
    ]);

    const responseData = {
      ...applicant,
      programName: program?.name || "Unknown Program",
      reviewer: reviewer
        ? {
            name: reviewer.name,
            email: reviewer.email,
            username: reviewer.username,
          }
        : null,
    };

    return NextResponse.json(responseData, { status: 200 });
  } catch (error) {
    console.error("Error fetching applicant:", error);
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}
