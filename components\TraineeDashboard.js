"use client"

import { useState } from "react"
import Link from "next/link"
import { LayoutDashboard,UserCheck2, File, ClipboardList, Users, Bar<PERSON>hart, User } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import Ward<PERSON><PERSON> from "./WardChart"
import ExportBeneficiariesModal from "./ExportBeneficiariesModal"
import TraineeOnly from "./TraineeOnly"
const navItems = [
  { href: "/programs", label: "Explore Programs", icon: LayoutDashboard },
  { id: "export-modal", label: "Export Beneficiaries", icon:  File, isModal: true },
  { href: "/applicants", label: "View Applications", icon: ClipboardList },
  { href: "/profile", label: "Update Profile", icon: UserCheck2 },
]

export default function AdminDashboard() {
  const [isModalOpen, setIsModalOpen] = useState(false)

  const openModal = () => {
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
  }

  return (
    <TraineeOnly>
   
    
      <Card className="border-2 shadow-2xl bg-background dark:border-gray-800">
        <CardHeader className="border-b-2 border-gray-800">  
          <CardTitle className="font-bold mb-3">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4">
            {navItems.map((item) => (
              item.isModal ? (
                <div 
                  key={item.id} 
                  onClick={openModal}
                  className="shadow-md p-6 rounded-xl transition-colors bg-card text-white flex flex-col items-center justify-center gap-4 cursor-pointer hover:bg-gray-400 dark:hover:bg-slate-800"
                >
                  <item.icon size={28} />
                  <h2 className="text-sm md:text-md font-mono text-center font-semibold">{item.label}</h2>
                </div>
              ) : (
                <Link key={item.href} href={item.href}>
                  <div className="shadow-md p-6 rounded-xl transition-colors bg-card text-white flex flex-col items-center justify-center gap-4 cursor-pointer hover:bg-gray-400 dark:hover:bg-slate-800">
                    <item.icon size={28} />
                    <h2 className="text-sm md:text-md font-mono text-center font-semibold">{item.label}</h2>
                  </div>
                </Link>
              )
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Export Beneficiaries Modal */}
      <ExportBeneficiariesModal isOpen={isModalOpen} onClose={closeModal} />
      
      <div className="mt-8">
        <WardChart/>
      </div>
  
    </TraineeOnly>
  )
}