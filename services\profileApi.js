// services/profileApi.js
import axios from "axios"
import { z } from "zod"

// Profile update validation schema
const profileUpdateSchema = z
  .object({
    name: z
      .string()
      .min(2, "Name must be at least 2 characters")
      .max(100, "Name must be less than 100 characters")
      .optional(),
    email: z.string().email("Invalid email format").optional(),
    phone: z
      .string()
      .regex(/^\+?[0-9]{8,15}$/, "Phone number must be between 8-15 digits")
      .optional(),
    bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
  })
  .refine((data) => Object.keys(data).length > 0, {
    message: "At least one field must be provided for update",
  })

// Password change validation schema - simplified for API use
const passwordApiSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(6, "New password must be at least 6 characters"),
})

// API functions using axios
export const profileApi = {
  getProfile: async () => {
    const response = await axios.get("/api/users/profile")
    return response.data.data
  },
  updateProfile: async (profileData) => {
    // Validate data before sending to API
    const validatedData = profileUpdateSchema.parse(profileData)
    const response = await axios.put("/api/users/profile/update", validatedData)
    return response.data
  },
  changePassword: async (passwordData) => {
    // Validate only the fields the API expects
    const validatedData = passwordApiSchema.parse(passwordData)
    const response = await axios.put("/api/users/profile/change-password", validatedData)
    return response.data
  },
}