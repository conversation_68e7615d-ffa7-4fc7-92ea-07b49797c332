'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import axios from 'axios';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { wards } from '@/lib/wards';
import { Lock, Unlock, ArrowLeft } from 'lucide-react';

export default function WardSelectionPage() {
  const params = useParams();
  const router = useRouter();
  const programId = params.programId;
  
  const [isLoading, setIsLoading] = useState(false);
  const [wardPercentages, setWardPercentages] = useState({});
  const [lockedWards, setLockedWards] = useState({});
  const [wardStats, setWardStats] = useState([]);
  const [loadingWardStats, setLoadingWardStats] = useState(false);
  const [programMaxBeneficiaries, setProgramMaxBeneficiaries] = useState(0);
  const [programName, setProgramName] = useState('');
  const queryClient = useQueryClient();

  // Fetch ward statistics and program details when page loads
  const fetchWardStats = async () => {
    setLoadingWardStats(true);
    try {
      // Fetch ward statistics
      const wardStatsResponse = await axios.get(`/api/programs/ward-stats/${programId}`);
      
      // Fetch program details to get maxBeneficiaries and name
      const programResponse = await axios.get(`/api/programs/fetch/${programId}?fields=maxBeneficiaries,name`);
      
      console.log('API Responses:', {
        wardStats: wardStatsResponse.data,
        program: programResponse.data,
        maxBeneficiaries: programResponse.data?.program?.maxBeneficiaries
      });
      
      if (wardStatsResponse.data.success) {
        setWardStats(wardStatsResponse.data.data.wardStats);
      }
      
      if (programResponse.data && programResponse.data.program) {
        setProgramMaxBeneficiaries(programResponse.data.program.maxBeneficiaries);
        setProgramName(programResponse.data.program.name);
        console.log('Set maxBeneficiaries to:', programResponse.data.program.maxBeneficiaries);
      } else {
        console.error('Program data not found in response:', programResponse.data);
      }
      
      // Initialize ward percentages with equal distribution
      const equalPercentage = Math.round(100 / wards.length * 100) / 100;
      const initialPercentages = {};
      wards.forEach(ward => {
        initialPercentages[ward] = equalPercentage;
      });
      
      // Adjust the last ward to make total exactly 100%
      const totalInitial = Object.values(initialPercentages).reduce((sum, pct) => sum + pct, 0);
      const lastWard = wards[wards.length - 1];
      initialPercentages[lastWard] = Math.round((initialPercentages[lastWard] + (100 - totalInitial)) * 100) / 100;
      
      setWardPercentages(initialPercentages);
      
    } catch (error) {
      console.error('Error fetching ward stats:', error);
      toast.error('Failed to load ward statistics');
    } finally {
      setLoadingWardStats(false);
    }
  };

  // Load data when component mounts
  useEffect(() => {
    fetchWardStats();
  }, []);

  // Toggle lock status for a ward
  const toggleWardLock = (ward) => {
    setLockedWards(prev => ({
      ...prev,
      [ward]: !prev[ward]
    }));
  };

  // Handle percentage change for a ward with automatic redistribution
  const handlePercentageChange = (ward, value) => {
    let numValue = Math.max(0, parseFloat(value) || 0);
    
    // Calculate total of locked wards (excluding current ward)
    const lockedWards_filtered = Object.keys(lockedWards).filter(w => lockedWards[w] && w !== ward);
    const lockedTotal = lockedWards_filtered.reduce((sum, w) => sum + (wardPercentages[w] || 0), 0);
    
    // Calculate maximum allowed value for current ward
    const maxAllowed = 100 - lockedTotal;
    
    // Clamp the value to not exceed what's available
    numValue = Math.min(numValue, maxAllowed);
    
    setWardPercentages(prev => {
      const newPercentages = { ...prev };
      newPercentages[ward] = numValue;
      
      // Calculate remaining percentage to distribute among other wards
      const remainingPercentage = 100 - numValue - lockedTotal;
      
      // Get wards that can be redistributed (not locked and not the current ward)
      const redistributableWards = wards.filter(w => 
        w !== ward && !lockedWards_filtered.includes(w)
      );
      
      if (redistributableWards.length === 0) {
        return newPercentages;
      }
      
      if (remainingPercentage <= 0) {
        // If no remaining percentage, set all redistributable wards to 0
        redistributableWards.forEach(w => {
          newPercentages[w] = 0;
        });
      } else {
        // Calculate current total of redistributable wards
        const currentRedistributableTotal = redistributableWards.reduce((sum, w) => sum + (prev[w] || 0), 0);
        
        if (currentRedistributableTotal === 0) {
          // If redistributable wards have no percentage, distribute equally
          const equalShare = remainingPercentage / redistributableWards.length;
          redistributableWards.forEach(w => {
            newPercentages[w] = Math.round(equalShare * 100) / 100;
          });
        } else {
          // Redistribute proportionally based on current percentages
          redistributableWards.forEach(w => {
            const currentPercentage = prev[w] || 0;
            const proportion = currentPercentage / currentRedistributableTotal;
            newPercentages[w] = Math.round(remainingPercentage * proportion * 100) / 100;
          });
        }
      }
      
      // Ensure total is exactly 100% by adjusting the last redistributable ward if needed
      const newTotal = Object.values(newPercentages).reduce((sum, pct) => sum + pct, 0);
      const difference = 100 - newTotal;
      
      if (Math.abs(difference) > 0.01 && redistributableWards.length > 0) {
        const lastRedistributableWard = redistributableWards[redistributableWards.length - 1];
        newPercentages[lastRedistributableWard] = Math.max(0, Math.round((newPercentages[lastRedistributableWard] + difference) * 100) / 100);
      }
      
      return newPercentages;
    });
  };

  // Auto-distribute remaining percentage
  const autoDistribute = () => {
    const equalPercentage = Math.round(100 / wards.length * 100) / 100;
    const newPercentages = {};
    wards.forEach(ward => {
      newPercentages[ward] = equalPercentage;
    });
    
    // Adjust the last ward to make total exactly 100%
    const totalInitial = Object.values(newPercentages).reduce((sum, pct) => sum + pct, 0);
    const lastWard = wards[wards.length - 1];
    newPercentages[lastWard] = Math.round((newPercentages[lastWard] + (100 - totalInitial)) * 100) / 100;
    
    setWardPercentages(newPercentages);
    // Clear all locks when auto-distributing
    setLockedWards({});
  };

  // Calculate total percentage
  const totalPercentage = Object.values(wardPercentages).reduce((sum, pct) => sum + pct, 0);

  // Calculate preview of selected applicants per ward
  const calculateSelectionPreview = () => {
    if (!programMaxBeneficiaries || Object.keys(wardPercentages).length === 0) {
      return {};
    }

    const totalSlots = programMaxBeneficiaries;
    const preview = {};
    let totalAllocated = 0;

    // First pass: Calculate base allocations using floor, limited by available applicants
    wards.forEach(ward => {
      const percentage = wardPercentages[ward] || 0;
      const exactSlots = (percentage / 100) * totalSlots;
      const baseSlots = Math.floor(exactSlots);
      const wardStat = wardStats.find(s => s.ward === ward);
      const availableApplicants = wardStat?.count || 0;
      
      // Limit allocation to available applicants
      const actualBaseSlots = Math.min(baseSlots, availableApplicants);
      
      preview[ward] = {
        percentage,
        exactSlots,
        originalAllocation: baseSlots,
        allocatedSlots: actualBaseSlots,
        remainder: exactSlots - baseSlots,
        availableApplicants,
        limitedByAvailability: actualBaseSlots < baseSlots,
        surplusSlots: baseSlots - actualBaseSlots, // Slots that couldn't be filled
        bonusSlots: 0 // Extra slots received from other wards
      };
      totalAllocated += actualBaseSlots;
    });

    // Calculate total surplus slots from wards that couldn't fill their allocation
    const totalSurplusSlots = Object.values(preview).reduce((sum, ward) => sum + ward.surplusSlots, 0);

    // Second pass: Distribute remaining slots (from rounding) based on largest remainders
    const remainingSlots = totalSlots - totalAllocated;
    if (remainingSlots > 0) {
      const wardsWithRemainders = wards
        .filter(ward => {
          const percentage = wardPercentages[ward] || 0;
          const wardPreview = preview[ward];
          // Only include wards with percentage > 0 AND have available applicants left
          return percentage > 0 && wardPreview.allocatedSlots < wardPreview.availableApplicants;
        })
        .sort((a, b) => preview[b].remainder - preview[a].remainder);

      let slotsToDistribute = remainingSlots;
      for (let i = 0; i < wardsWithRemainders.length && slotsToDistribute > 0; i++) {
        const ward = wardsWithRemainders[i];
        const wardPreview = preview[ward];
        
        // Only add if we haven't reached the available limit
        if (wardPreview.allocatedSlots < wardPreview.availableApplicants) {
          preview[ward].allocatedSlots += 1;
          preview[ward].bonusSlots += 1;
          slotsToDistribute--;
        }
      }
    }

    // Third pass: Redistribute surplus slots from wards that couldn't fill their allocation
    if (totalSurplusSlots > 0) {
      // Get wards that can accept more applicants, prioritizing by percentage
      const wardsCanAcceptMore = wards
        .filter(ward => {
          const percentage = wardPercentages[ward] || 0;
          const wardPreview = preview[ward];
          return percentage > 0 && wardPreview.allocatedSlots < wardPreview.availableApplicants;
        })
        .sort((a, b) => (wardPercentages[b] || 0) - (wardPercentages[a] || 0)); // Sort by percentage descending

      let surplusToDistribute = totalSurplusSlots;
      let roundRobin = 0;
      
      while (surplusToDistribute > 0 && wardsCanAcceptMore.length > 0) {
        const ward = wardsCanAcceptMore[roundRobin % wardsCanAcceptMore.length];
        const wardPreview = preview[ward];
        
        // Check if we can still add more without exceeding total slots
        const currentTotal = Object.values(preview).reduce((sum, w) => sum + w.allocatedSlots, 0);
        
        if (wardPreview.allocatedSlots < wardPreview.availableApplicants && currentTotal < totalSlots) {
          preview[ward].allocatedSlots += 1;
          preview[ward].bonusSlots += 1;
          surplusToDistribute--;
        } else {
          // Remove this ward from consideration if it's full or we've reached total limit
          wardsCanAcceptMore.splice(roundRobin % wardsCanAcceptMore.length, 1);
          continue;
        }
        
        roundRobin++;
      }
    }

    return preview;
  };

  const selectionPreview = calculateSelectionPreview();

  const handleConfirmSelection = async () => {
    setIsLoading(true);
  
    try {
      // Validate percentages add up to 100%
      if (Math.abs(totalPercentage - 100) > 0.01) {
        toast.error('Ward percentages must add up to 100%');
        setIsLoading(false);
        return;
      }
      
      const response = await axios.post(`/api/selection/ward-percentage/${programId}`, {
        wardPercentages
      });
  
      if (response.status === 200) {
        toast.success(response.data.message || 'Selection successful');
  
        // Invalidate and immediately refetch the program details query
        await queryClient.invalidateQueries({
          queryKey: ['programDetails', programId],
        });
        await queryClient.invalidateQueries({
          queryKey: [ "ward-stats"],
        });
         
        await queryClient.invalidateQueries({
          queryKey: ['beneficiaries'],
        });
  
        await queryClient.refetchQueries({
          queryKey: ['programDetails', programId],
        });
        await queryClient.refetchQueries({
          queryKey:["programApplicants", programId],
        });
        
        // Navigate back to program details
        router.push(`/programs/programdetails/${programId}`);
         
      } else {
        toast.error(`${response.data.error || 'Unexpected error occurred'}`);
      }
    } catch (error) {
      if (error.response) {
        toast.error(`Error: ${error.response.data.error || 'Unknown error'}`);
      } else {
        toast.error('Network Error: Failed to select applicants');
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (loadingWardStats) {
    return (
      <div className="min-h-screen bg-background text-white p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header Skeleton */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="skeleton h-8 w-32 bg-gray-700"></div>
              <div>
                <div className="skeleton h-8 w-64 bg-gray-700 mb-2"></div>
                <div className="skeleton h-4 w-40 bg-gray-600"></div>
              </div>
            </div>
            <div className="flex gap-3">
              <div className="skeleton h-10 w-20 bg-gray-700"></div>
              <div className="skeleton h-10 w-32 bg-gray-700"></div>
            </div>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
            {/* Left Column - Configuration Skeleton */}
            <div className="xl:col-span-2">
              <div className="bg-card border border-slate-700 rounded-lg p-6">
                <div className="flex justify-between items-center mb-6">
                  <div className="skeleton h-6 w-48 bg-gray-700"></div>
                  <div className="flex items-center gap-4">
                    <div className="skeleton h-6 w-24 bg-gray-700"></div>
                    <div className="skeleton h-8 w-28 bg-gray-700"></div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Array.from({ length: 12 }).map((_, index) => (
                    <div key={index} className="border border-slate-600 rounded-lg p-4">
                      <div className="mb-3">
                        <div className="skeleton h-5 w-32 bg-gray-700 mb-1"></div>
                      </div>

                      <div className="flex items-center gap-3">
                        <div className="skeleton h-8 w-8 bg-gray-700"></div>
                        <div className="flex items-center gap-2 flex-1">
                          <div className="skeleton h-8 w-20 bg-gray-700"></div>
                          <div className="skeleton h-4 w-4 bg-gray-700"></div>
                        </div>
                        <div className="text-right flex-1">
                          <div className="skeleton h-6 w-24 bg-gray-700 mb-1"></div>
                          <div className="skeleton h-4 w-20 bg-gray-600"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Column - Preview Skeleton */}
            <div className="xl:col-span-1">
              <div className="bg-slate-800 rounded-lg border border-slate-600 p-6 sticky top-6">
                <div className="skeleton h-6 w-32 bg-gray-700 mb-4 mx-auto"></div>
                <div className="skeleton h-6 w-48 bg-gray-700 mb-4 mx-auto"></div>

                <div className="grid grid-cols-2 gap-3 max-h-96 overflow-y-auto">
                  {Array.from({ length: 12 }).map((_, index) => (
                    <div key={index} className="p-3 rounded-xl border border-gray-600 bg-gray-800/20">
                      <div className="skeleton h-4 w-24 bg-gray-700 mb-2"></div>
                      <div className="flex items-center gap-2 mb-1">
                        <div className="skeleton h-6 w-8 bg-gray-700"></div>
                        <div className="skeleton h-4 w-8 bg-gray-700"></div>
                      </div>
                      <div className="skeleton h-3 w-20 bg-gray-600 mb-1"></div>
                      <div className="skeleton h-3 w-16 bg-gray-600"></div>
                    </div>
                  ))}
                </div>

                <div className="flex gap-3 mt-6">
                  <div className="skeleton h-10 w-full bg-gray-700"></div>
                  <div className="skeleton h-10 w-full bg-gray-700"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.push(`/programs/programdetails/${programId}`)}
              className="btn btn-sm bg-gray-700 hover:bg-gray-600 border-none"
            >
              <ArrowLeft size={16} />
              Back to Program
            </button>
            <div>
              <h1 className="text-3xl font-bold">Ward-Based Selection</h1>
              <p className="text-gray-400">{programName}</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              className="btn bg-gray-700 hover:bg-gray-600 border-none"
              onClick={() => router.push(`/programs/programdetails/${programId}`)}
            >
              Cancel
            </button>
            <button
              className="btn bg-green-700 hover:bg-green-600 border-none"
              onClick={handleConfirmSelection}
              disabled={isLoading || Math.abs(totalPercentage - 100) > 0.01}
            >
              {isLoading ? 'Processing...' : 'Confirm Selection'}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Left Column - Ward Configuration */}
          <div className="xl:col-span-2">
            <div className="bg-card border border-slate-700 rounded-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold">Configure Ward Percentages</h2>
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <span className={`font-bold text-lg ${Math.abs(totalPercentage - 100) < 0.01 ? 'text-green-400' : 'text-red-400'}`}>
                      Total: {totalPercentage.toFixed(1)}%
                    </span>
                  </div>
                  <button
                    className="btn btn-sm bg-blue-700 hover:bg-blue-600 border-none"
                    onClick={autoDistribute}
                  >
                    Auto Distribute
                  </button>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {wardStats.map((stat) => {
                  // Calculate max allowed for this ward
                  const lockedWards_filtered = Object.keys(lockedWards).filter(w => lockedWards[w] && w !== stat.ward);
                  const lockedTotal = lockedWards_filtered.reduce((sum, w) => sum + (wardPercentages[w] || 0), 0);
                  const maxAllowed = 100 - lockedTotal;
                  
                  return (
                    <div key={stat.ward} className="border border-slate-600 rounded-lg p-4">
                      <div className="mb-3">
                        <h4 className="font-bold text-base text-white mb-1">{stat.ward}</h4>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        {/* Lock Toggle Button */}
                        <button
                          onClick={() => toggleWardLock(stat.ward)}
                          className={`btn btn-sm ${
                            lockedWards[stat.ward] 
                              ? 'bg-yellow-600 hover:bg-yellow-700 text-white' 
                              : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                          }`}
                          title={lockedWards[stat.ward] ? 'Unlock percentage' : 'Lock percentage'}
                        >
                          {lockedWards[stat.ward] ? <Lock size={14} /> : <Unlock size={14} />}
                        </button>
                        
                        {/* Hybrid Slider + Input Section */}
                        <div className="flex flex-col gap-3 flex-1">
                          {/* Custom Slider */}
                          <div className="flex items-center gap-3">
                            {/* Custom Slider Container */}
                            <div className="relative flex-1 h-6 flex items-center">
                              {/* Track Background */}
                              <div className={`absolute w-full h-2 rounded-full ${
                                lockedWards[stat.ward]
                                  ? 'bg-yellow-800/30'
                                  : 'bg-gray-700'
                              }`}></div>

                              {/* Progress Fill */}
                              <div
                                className={`absolute h-2 rounded-full transition-all duration-200 ${
                                  lockedWards[stat.ward]
                                    ? 'bg-yellow-500/60'
                                    : 'bg-gradient-to-r from-blue-500 to-cyan-400'
                                }`}
                                style={{
                                  width: `${Math.min(((wardPercentages[stat.ward] || 0) / maxAllowed) * 100, 100)}%`
                                }}
                              ></div>

                              {/* Slider Input (Invisible) */}
                              <input
                                type="range"
                                min="0"
                                max={maxAllowed}
                                step="0.1"
                                value={wardPercentages[stat.ward] || 0}
                                onChange={(e) => handlePercentageChange(stat.ward, e.target.value)}
                                className="absolute w-full h-6 opacity-0 cursor-pointer disabled:cursor-not-allowed"
                                disabled={lockedWards[stat.ward]}
                                title={`Drag to adjust percentage (Max: ${maxAllowed.toFixed(1)}%)`}
                              />

                              {/* Slider Thumb */}
                              <div
                                className={`absolute w-5 h-5 rounded-full border-2 transition-all duration-200 transform -translate-x-1/2 ${
                                  lockedWards[stat.ward]
                                    ? 'bg-yellow-400 border-yellow-300 shadow-lg shadow-yellow-500/30'
                                    : 'bg-white border-blue-400 shadow-lg shadow-blue-500/30 hover:scale-110'
                                } ${lockedWards[stat.ward] ? '' : 'hover:shadow-xl hover:shadow-blue-500/50'}`}
                                style={{
                                  left: `${Math.min(((wardPercentages[stat.ward] || 0) / maxAllowed) * 100, 100)}%`,
                                  pointerEvents: 'none'
                                }}
                              ></div>
                            </div>
                          </div>

                          {/* Input Field Row */}
                          <div className="flex items-center gap-2 flex-wrap">
                            <div className="flex items-center gap-2 flex-shrink-0">
                              <input
                                type="number"
                                min="0"
                                max={maxAllowed}
                                step="0.1"
                                value={wardPercentages[stat.ward] || 0}
                                onChange={(e) => handlePercentageChange(stat.ward, e.target.value)}
                                className={`input input-sm text-white w-16 transition-all duration-200 ${
                                  lockedWards[stat.ward]
                                    ? 'bg-yellow-900/50 border-yellow-600/50 text-yellow-200'
                                    : 'bg-gray-800 border-gray-600 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
                                }`}
                                placeholder="0.0"
                                disabled={lockedWards[stat.ward]}
                                title={`Type percentage directly (Max: ${maxAllowed.toFixed(1)}%)`}
                              />
                              <span className="text-sm font-medium text-gray-300">%</span>
                            </div>

                            {/* Percentage Display Badge */}
                            <div className={`text-xs font-bold px-2 py-1 rounded border transition-all duration-200 flex-shrink-0 ${
                              lockedWards[stat.ward]
                                ? 'bg-yellow-900/30 text-yellow-300 border-yellow-600/30'
                                : 'bg-blue-900/30 text-blue-300 border-blue-600/30'
                            }`}>
                              {(wardPercentages[stat.ward] || 0).toFixed(1)}%
                            </div>
                          </div>

                          {/* Max Value Indicator - Separate Row */}
                          {!lockedWards[stat.ward] && maxAllowed < 100 && (
                            <div className="flex items-center gap-1 text-xs text-orange-400 mt-1">
                              <div className="w-1.5 h-1.5 bg-orange-400 rounded-full"></div>
                              <span>Max available: {maxAllowed.toFixed(1)}%</span>
                            </div>
                          )}
                        </div>
                        
                        {/* Statistics Section */}
                        <div className="text-right flex-1">
                          <div className="text-lg font-bold text-blue-400">
                            {stat.count} applicants
                          </div>
                          <div className="text-sm text-gray-300">
                            ({stat.percentage}% of total)
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Right Column - Selection Preview */}
          <div className="xl:col-span-1">
            {programMaxBeneficiaries > 0 && Object.keys(selectionPreview).length > 0 && (
              <div className="bg-slate-800 rounded-lg border border-slate-600 p-6 sticky top-6">
                <h3 className="font-bold text-xl mb-4 text-center">Selection Preview</h3>
                <div className="text-center mb-4">
                  <span className="text-xl font-bold text-green-400">
                    Total: {Object.values(selectionPreview).reduce((sum, ward) => sum + ward.allocatedSlots, 0)} / {programMaxBeneficiaries} applicants
                  </span>
                </div>
                
                <div className="grid grid-cols-2 gap-3 max-h-96 overflow-y-auto">
                  {wards.map(ward => {
                    const preview = selectionPreview[ward];
                    const wardStat = wardStats.find(s => s.ward === ward);
                    const available = wardStat?.count || 0;
                    const willSelect = preview?.allocatedSlots || 0;
                    const originalAllocation = preview?.originalAllocation || 0;
                    const bonusSlots = preview?.bonusSlots || 0;
                    const surplusSlots = preview?.surplusSlots || 0;

                    return (
                      <div key={ward} className={`p-3 rounded-xl border ${
                        willSelect > 0 ? 'border-green-700 bg-green-900/20' : 'border-gray-600 bg-gray-800/20'
                      }`}>
                        <div className="font-medium text-sm truncate mb-2" title={ward}>
                          {ward}
                        </div>

                        <div className="flex items-center gap-2 mb-1">
                          <div className={`text-lg font-bold ${willSelect > 0 ? 'text-green-400' : 'text-gray-500'}`}>
                            {willSelect}
                          </div>
                          {bonusSlots > 0 && (
                            <div className="text-xs bg-blue-600 text-white px-2 py-1 rounded" title={`+${bonusSlots} bonus slots from other wards`}>
                              +{bonusSlots}
                            </div>
                          )}
                        </div>

                        <div className="text-xs text-gray-400 mb-1">
                          of {available} available
                        </div>

                        {originalAllocation !== willSelect && (
                          <div className="text-xs text-blue-300">
                            {originalAllocation > willSelect
                              ? `${surplusSlots} slots redistributed`
                              : `Original: ${originalAllocation}`
                            }
                          </div>
                        )}

                        {willSelect > available && (
                          <div className="text-xs text-red-400 font-bold">
                            ⚠️ Exceeds available!
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
                
                {Math.abs(totalPercentage - 100) > 0.01 && (
                  <div className="text-center mt-4 text-orange-400 text-sm">
                    ⚠️ Percentages don't add up to 100% - preview may be inaccurate
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
