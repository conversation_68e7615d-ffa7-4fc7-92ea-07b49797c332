import { NextResponse } from "next/server";
import mongoose from "mongoose";
import Program from "@/models/Program";
import ProgramForm from "@/models/ProgramForm";
import Applicant from "@/models/Applicant";
import Beneficiary from "@/models/Beneficiary";
import { dbConnect } from "@/lib/db";
import { withAdminAuth } from "@/middleware/authMiddleware";
import { rateLimit } from "@/lib/ratelimit";
import { createLogger } from "@/lib/logger";
import { sanitizeInput } from "@/lib/sanitize";

// Create a custom logger for this route
const logger = createLogger({ service: "program-delete" });

// Define the main handler function
async function deleteProgramHandler(req, { params }) {
  try {
    await dbConnect();

    // Await params first (Next.js 15 requirement)
    const awaitedParams = await params;

    // Sanitize params
    const sanitizedParams = sanitizeInput(awaitedParams);
    const { id } = sanitizedParams;

    if (!id) {
      logger.warn("Missing program ID in request", { params: awaitedParams });
      return NextResponse.json({ error: "Program ID is required" }, { status: 400 });
    }

    // Ensure ID is a valid MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      logger.warn("Invalid program ID format", { programId: id });
      return NextResponse.json({ error: "Invalid Program ID" }, { status: 400 });
    }

    logger.info("Processing program deletion request", { programId: id });

    // Check if program exists
    const program = await Program.findById(id);
    if (!program) {
      logger.warn("Program not found for deletion", { programId: id });
      return NextResponse.json({ error: "Program not found" }, { status: 404 });
    }

    // Delete all related data sequentially (no transactions needed for development)
    try {
      // Delete all applicants for this program
      const applicantsResult = await Applicant.deleteMany({ programId: id });
      logger.info("Deleted applicants", {
        programId: id,
        deletedCount: applicantsResult.deletedCount
      });

      // Delete all beneficiaries for this program
      const beneficiariesResult = await Beneficiary.deleteMany({ programId: id });
      logger.info("Deleted beneficiaries", {
        programId: id,
        deletedCount: beneficiariesResult.deletedCount
      });

      // Delete the program form if it exists
      const programFormResult = await ProgramForm.deleteOne({ programid: id });
      logger.info("Deleted program form", {
        programId: id,
        deletedCount: programFormResult.deletedCount
      });

      // Finally, delete the program itself
      await Program.findByIdAndDelete(id);
      logger.info("Deleted program", { programId: id });

      logger.info("Program deletion completed successfully", {
        programId: id,
        programName: program.name
      });

      return NextResponse.json(
        {
          message: "Program and all related data deleted successfully",
          deletedProgram: {
            id: program._id,
            name: program.name
          }
        },
        { status: 200 }
      );

    } catch (deletionError) {
      logger.error("Error during program deletion", {
        programId: id,
        error: deletionError.message,
        stack: deletionError.stack
      });
      throw deletionError;
    }

  } catch (error) {
    logger.error("Error deleting program", {
      programId: id || "unknown",
      error: error.message,
      stack: error.stack
    });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// Apply rate limiting - 5 requests per minute (more restrictive for delete operations)
const deleteProgramRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // 5 requests per minute
  keyPrefix: "program-delete"
});

// Export the secured DELETE handler with rate limiting and admin auth
export async function DELETE(req, { params }) {
  // Apply rate limiting first
  const rateLimitResult = await deleteProgramRateLimit(req);
  if (!rateLimitResult.success) {
    logger.warn("Rate limit exceeded for program deletion", {
      path: req.nextUrl.pathname,
      ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown"
    });
    return rateLimitResult.response;
  }
  
  // Then apply admin authentication
  return withAdminAuth(deleteProgramHandler)(req, { params });
}
