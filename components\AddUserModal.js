"use client";

import { useRouter } from "next/navigation";
import { toast } from "sonner";
import axios from "axios";
import { useQueryClient } from "@tanstack/react-query";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { z } from "zod";
import { toFormikValidationSchema } from "zod-formik-adapter";
import { useState } from "react";
import { UserPlus, User, AtSign, Phone, Loader2, X, Check, Plus } from "lucide-react";

// Zod validation schema (no password)
const userSchema = z.object({
  name: z.string().min(2, "Name is required"),
  username: z.string()
    .min(3, "Username too short")
    .max(25, "Username is too long")
    // Updated regex pattern that allows email format but prevents spaces and NoSQL injection
    .regex(/^[a-zA-Z0-9_.+-]+@?[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/, "Username cannot end with spaces or special characters")
    .refine(val => !val.includes(" "), {
      message: "Username cannot contain spaces"
    }),
  phone: z
    .string()
    .min(7, "Phone number too short")
    .max(19, "Phone number too long"),
});

export default function AddUserModal() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const initialValues = {
    name: "",
    username: "",
    phone: "",
  };

  const handleSubmit = async (values, { resetForm }) => {
    setLoading(true);
    try {
      await axios.post("/api/users/create", values);
      toast.success("User added successfully!");
      closeModal();
      await queryClient.invalidateQueries(["users"]);
      await queryClient.refetchQueries(["users"]);
      resetForm();
    } catch (error) {
      const message = error.response?.data?.error || "Failed to add user.";
      toast.error(message);
    }
    setLoading(false);
  };

  const openModal = () => {
    document.getElementById("add-user-modal").showModal();
    setIsOpen(true);
  };

  const closeModal = () => {
    document.getElementById("add-user-modal").close();
    setIsOpen(false);
  };

  return (
    <>
      {/* Trigger Button */}
      <button
        className="btn rounded-lg border w-full bg-lime-600 hover:bg-lime-700 text-white font-medium transition-colors shadow-md flex items-center justify-center gap-2"
        onClick={openModal}
      >
        <UserPlus className="h-5 w-5" />
        Add New User
      </button>

      {/* Modal */}
      <dialog id="add-user-modal" className="modal  backdrop-blur-sm bg-opacity-0">
        <div className="modal-box bg-card border border-slate-700 rounded-lg shadow-xl max-w-md w-full p-0">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="font-bold text-xl text-gray-800 dark:text-white flex items-center gap-2">
                <UserPlus className="h-5 w-5 text-lime-600" />
                Add New User
              </h3>
              <button 
                onClick={closeModal}
                className="btn btn-sm btn-circle bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 border-none"
              >
                <X className="h-4 w-4 text-gray-500 dark:text-gray-400" />
              </button>
            </div>
          </div>
          
          <Formik
            initialValues={initialValues}
            validationSchema={toFormikValidationSchema(userSchema)}
            onSubmit={handleSubmit}
          >
            {({ errors, touched }) => (
              <Form className="p-6 space-y-4">
                <div className="form-control w-full">
                  <label className="label">
                    <span className="label-text text-white  font-medium">Full Name</span>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <User className="h-5 w-5 text-gray-400" />
                    </div>
                    <Field
                      name="name"
                      type="text"
                      placeholder="Enter full name"
                      className={`pl-10 pr-4 py-2 w-full rounded-lg border ${
                        errors.name && touched.name 
                          ? "border-red-500 focus:ring-red-500" 
                          : "border-gray-300 dark:border-gray-600 focus:ring-lime-500"
                      } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2`}
                    />
                  </div>
                  <ErrorMessage
                    name="name"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="form-control w-full">
                  <label className="label">
                    <span className="label-text text-white font-medium">Username</span>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <AtSign className="h-5 w-5 text-gray-400" />
                    </div>
                    <Field
                      name="username"
                      type="text"
                      placeholder="Enter username"
                      className={`pl-10 pr-4 py-2 w-full rounded-lg border ${
                        errors.username && touched.username 
                          ? "border-red-500 focus:ring-red-500" 
                          : "border-gray-300 dark:border-gray-600 focus:ring-lime-500"
                      } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2`}
                    />
                  </div>
                  <ErrorMessage
                    name="username"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="form-control w-full">
                  <label className="label">
                    <span className="label-text text-white  font-medium">Phone Number</span>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <Phone className="h-5 w-5 text-gray-400" />
                    </div>
                    <Field
                      name="phone"
                      type="tel"
                      placeholder="Enter phone number"
                      className={`pl-10 pr-4 py-2 w-full rounded-lg border ${
                        errors.phone && touched.phone 
                          ? "border-red-500 focus:ring-red-500" 
                          : "border-gray-300 dark:border-gray-600 focus:ring-lime-500"
                      } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2`}
                    />
                  </div>
                  <ErrorMessage
                    name="phone"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="flex justify-end gap-3 pt-4">
                  <button
                    type="button"
                    className="btn px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center gap-2"
                    onClick={closeModal}
                  >
                    <X className="h-4 w-4" /> Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn px-4 py-2 bg-lime-600 hover:bg-lime-700 text-white rounded-lg transition-colors flex items-center gap-2 disabled:opacity-70"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" /> Processing...
                      </>
                    ) : (
                      <>
                        <Check className="h-4 w-4" /> Add User
                      </>
                    )}
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
        <form method="dialog" className="modal-backdrop">
          <button onClick={closeModal}>close</button>
        </form>
      </dialog>
    </>
  );
}