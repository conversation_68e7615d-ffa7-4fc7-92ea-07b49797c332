{"level":"warn","message":"Login failed: Incorrect password","metadata":{"service":"auth","userId":"67ea54984863f11a02e85b7d","username":"<EMAIL>"},"timestamp":"2025-05-12 10:36:11"}
{"level":"error","message":"Login error","metadata":{"error":"Invalid username or password","service":"auth","username":"<EMAIL>"},"timestamp":"2025-05-12 10:36:11"}
{"level":"info","message":"User logged in successfully","metadata":{"service":"auth","userId":"67ea54984863f11a02e85b7d","username":"<EMAIL>"},"timestamp":"2025-05-12 10:36:19"}
{"level":"info","message":"User logged in successfully","metadata":{"service":"auth","userId":"67ea54984863f11a02e85b7d","username":"<EMAIL>"},"timestamp":"2025-05-12 10:40:09"}
{"level":"info","message":"Program creation initiated","metadata":{"service":"api/programs"},"timestamp":"2025-05-12 10:48:40"}
{"level":"info","message":"Program created successfully","metadata":{"programId":"6821c3f84cac5d48a0a484e2","programName":"New program 2025","service":"api/programs"},"timestamp":"2025-05-12 10:48:40"}
{"level":"info","message":"Processing program form update","metadata":{"method":"PUT","programId":"6821c3f84cac5d48a0a484e2","service":"program-form-api"},"timestamp":"2025-05-12 10:49:30"}
{"level":"info","message":"Creating new program form","metadata":{"programId":"6821c3f84cac5d48a0a484e2","service":"program-form-api"},"timestamp":"2025-05-12 10:49:30"}
{"level":"info","message":"Program updated with new form ID","metadata":{"formId":"6821c42a4cac5d48a0a484f0","programId":"6821c3f84cac5d48a0a484e2","service":"program-form-api"},"timestamp":"2025-05-12 10:49:30"}
{"level":"info","message":"Received application submission request","metadata":{"ip":"::1","service":"api/applicant"},"timestamp":"2025-05-12 11:08:17"}
{"level":"error","message":"Error submitting application","metadata":{"error":"Bad control character in string literal in JSON at position 157","service":"api/applicant","stack":"SyntaxError: Bad control character in string literal in JSON at position 157\n    at JSON.parse (<anonymous>)\n    at parseJSONFromBytes (node:internal/deps/undici/undici:5472:19)\n    at successSteps (node:internal/deps/undici/undici:5454:27)\n    at fullyReadBody (node:internal/deps/undici/undici:4381:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async consumeBody (node:internal/deps/undici/undici:5463:7)\n    at async POST (webpack-internal:///(rsc)/./app/api/applicants/create/route.js:61:30)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\compiled\\next-server\\app-route.runtime.dev.js:26:33891)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\compiled\\next-server\\app-route.runtime.dev.js:26:41254)\n    at async doRender (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:2393:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:2430:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:1003:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\next-server.js:304:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:895:17)\n    at async C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\router-server.js:235:21)\n    at async handleRequest (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\router-server.js:426:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\router-server.js:450:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)"},"timestamp":"2025-05-12 11:08:17"}
{"level":"info","message":"Received application submission request","metadata":{"ip":"::1","service":"api/applicant"},"timestamp":"2025-05-12 11:08:26"}
{"level":"info","message":"Received JSON data","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:26"}
{"level":"info","message":"Creating 100 dummy applicants","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:26"}
{"level":"info","message":"Received application submission request","metadata":{"ip":"::1","service":"api/applicant"},"timestamp":"2025-05-12 11:08:30"}
{"level":"info","message":"Received JSON data","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:30"}
{"level":"info","message":"Creating 100 dummy applicants","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:30"}
{"level":"info","message":"Received application submission request","metadata":{"ip":"::1","service":"api/applicant"},"timestamp":"2025-05-12 11:08:32"}
{"level":"info","message":"Received JSON data","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:32"}
{"level":"info","message":"Creating 100 dummy applicants","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:32"}
{"level":"info","message":"Received application submission request","metadata":{"ip":"::1","service":"api/applicant"},"timestamp":"2025-05-12 11:08:37"}
{"level":"info","message":"Received JSON data","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:37"}
{"level":"info","message":"Creating 100 dummy applicants","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:37"}
{"level":"info","message":"Received application submission request","metadata":{"ip":"::1","service":"api/applicant"},"timestamp":"2025-05-12 11:08:39"}
{"level":"info","message":"Received JSON data","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:39"}
{"level":"info","message":"Creating 100 dummy applicants","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:39"}
{"level":"info","message":"Received application submission request","metadata":{"ip":"::1","service":"api/applicant"},"timestamp":"2025-05-12 11:08:44"}
{"level":"info","message":"Received JSON data","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:44"}
{"level":"info","message":"Creating 200 dummy applicants","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:44"}
{"level":"info","message":"Received application submission request","metadata":{"ip":"::1","service":"api/applicant"},"timestamp":"2025-05-12 11:08:47"}
{"level":"info","message":"Received JSON data","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:47"}
{"level":"info","message":"Creating 200 dummy applicants","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:47"}
{"level":"info","message":"Received application submission request","metadata":{"ip":"::1","service":"api/applicant"},"timestamp":"2025-05-12 11:08:50"}
{"level":"info","message":"Received JSON data","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:50"}
{"level":"info","message":"Creating 200 dummy applicants","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:50"}
{"level":"info","message":"Received application submission request","metadata":{"ip":"::1","service":"api/applicant"},"timestamp":"2025-05-12 11:08:53"}
{"level":"info","message":"Received JSON data","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:53"}
{"level":"info","message":"Creating 200 dummy applicants","metadata":{"service":"api/applicant"},"timestamp":"2025-05-12 11:08:53"}
{"level":"warn","message":"Login failed: User not found","metadata":{"service":"auth","username":"<EMAIL>"},"timestamp":"2025-05-12 11:16:17"}
{"level":"error","message":"Login error","metadata":{"error":"Invalid username or password","service":"auth","username":"<EMAIL>"},"timestamp":"2025-05-12 11:16:17"}
{"level":"warn","message":"Login failed: User not found","metadata":{"service":"auth","username":"<EMAIL>"},"timestamp":"2025-05-12 11:16:28"}
{"level":"error","message":"Login error","metadata":{"error":"Invalid username or password","service":"auth","username":"<EMAIL>"},"timestamp":"2025-05-12 11:16:28"}
{"level":"warn","message":"Login failed: User not found","metadata":{"service":"auth","username":"Mihawk"},"timestamp":"2025-05-12 11:16:58"}
{"level":"error","message":"Login error","metadata":{"error":"Invalid username or password","service":"auth","username":"Mihawk"},"timestamp":"2025-05-12 11:16:58"}
{"level":"info","message":"User logged in successfully","metadata":{"service":"auth","userId":"67ea54984863f11a02e85b7d","username":"<EMAIL>"},"timestamp":"2025-05-12 11:17:19"}
{"level":"warn","message":"Unauthorized access attempt","metadata":{"ip":"::ffff:************","path":"/api/programs/summary","service":"auth-middleware"},"timestamp":"2025-05-12 11:22:25"}
{"level":"warn","message":"Unauthorized access attempt","metadata":{"ip":"::ffff:************","path":"/api/programs/summary","service":"auth-middleware"},"timestamp":"2025-05-12 11:22:26"}
{"level":"warn","message":"Login failed: User not found","metadata":{"service":"auth","username":"musms"},"timestamp":"2025-05-12 11:25:47"}
{"level":"error","message":"Login error","metadata":{"error":"Invalid username or password","service":"auth","username":"musms"},"timestamp":"2025-05-12 11:25:47"}
{"level":"warn","message":"Rate limit exceeded","metadata":{"ip":"***************","service":"auth","username":"Musamhh"},"timestamp":"2025-05-12 11:26:24"}
{"level":"info","message":"User logged in successfully","metadata":{"service":"auth","userId":"67ea54984863f11a02e85b7d","username":"<EMAIL>"},"timestamp":"2025-05-12 11:34:46"}
{"level":"info","message":"User logged in successfully","metadata":{"service":"auth","userId":"67ea54984863f11a02e85b7d","username":"<EMAIL>"},"timestamp":"2025-05-12 11:35:25"}
{"level":"info","message":"Program creation initiated","metadata":{"service":"api/programs"},"timestamp":"2025-05-12 11:45:07"}
{"level":"info","message":"Program created successfully","metadata":{"programId":"6821d133cfbc52682ef2f6d0","programName":"testing 1","service":"api/programs"},"timestamp":"2025-05-12 11:45:07"}
{"level":"info","message":"Processing program form update","metadata":{"method":"PUT","programId":"6821d133cfbc52682ef2f6d0","service":"program-form-api"},"timestamp":"2025-05-12 11:46:08"}
{"level":"info","message":"Creating new program form","metadata":{"programId":"6821d133cfbc52682ef2f6d0","service":"program-form-api"},"timestamp":"2025-05-12 11:46:08"}
{"level":"info","message":"Program updated with new form ID","metadata":{"formId":"6821d170cfbc52682ef2f6de","programId":"6821d133cfbc52682ef2f6d0","service":"program-form-api"},"timestamp":"2025-05-12 11:46:08"}
