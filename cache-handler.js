const cache = new Map();

class CacheHandler {
  constructor(options) {
    this.options = options;
  }

  async get(key) {
    const data = cache.get(key);
    if (!data) return null;
    
    // Check if data is expired
    if (data.expiresAt && Date.now() > data.expiresAt) {
      cache.delete(key);
      return null;
    }
    
    return data.value;
  }

  async set(key, data, ctx) {
    // Set expiration based on tags
    let expiresAt = null;
    if (ctx.tags?.includes('program')) {
      expiresAt = Date.now() + (60 * 60 * 1000); // 1 hour for program data
    } else if (ctx.tags?.includes('applicant')) {
      expiresAt = Date.now() + (30 * 60 * 1000); // 30 minutes for applicant data
    } else {
      expiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours default
    }
    
    cache.set(key, {
      value: data,
      lastModified: Date.now(),
      expiresAt,
      tags: ctx.tags || [],
    });
  }

  async revalidateTag(tags) {
    tags = [tags].flat();
    for (let [key, value] of cache) {
      if (value.tags.some((tag) => tags.includes(tag))) {
        cache.delete(key);
      }
    }
  }
}

export default CacheHandler;
