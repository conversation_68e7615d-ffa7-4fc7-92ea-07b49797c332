import mongoose from "mongoose";
import { createLogger } from "@/lib/logger";

const logger = createLogger({ service: "database-connection" });
const mongoURI = process.env.MONGODB_URI;

const dbConnect = async () => {
    if (mongoose.connection.readyState === 1) {
        
        return;
    }

    try {
        await mongoose.connect(mongoURI);
        console.log("✅ MongoDB connected successfully");
    } catch (error) {
        logger.error("MongoDB connection failed", {
            error: error.message,
            stack: error.stack
        });
        throw new Error("Database connection failed");
    }
};

export { dbConnect };