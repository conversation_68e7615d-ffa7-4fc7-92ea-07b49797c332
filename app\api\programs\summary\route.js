import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import Program from "@/models/Program";
import Applicant from "@/models/Applicant";
import { withAuth } from "@/middleware/authMiddleware";
import { createLogger } from "@/lib/logger";

// Create a custom logger for this route
const logger = createLogger({ service: "programs-summary" });

// Define the main handler function
async function getProgramsSummaryHandler(req) {
  try {
    await dbConnect();
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "10", 10);
    const searchQuery = searchParams.get("search") || "";
    const statusFilter = searchParams.get("status") || "";
    const skip = (page - 1) * limit;

    

    // Get current date for status comparison
    const currentDate = new Date();

    // Update program statuses based on dates
    await updateProgramStatuses(currentDate);

    // Build filter query
    const filter = {};
    if (searchQuery) {
      filter.name = { $regex: searchQuery, $options: "i" }; // Case-insensitive search
    }
    if (statusFilter) {
      filter.status = statusFilter;
    }

    // Fetch total number of programs (unfiltered)
    const totalProgramsCount = await Program.countDocuments({});

    // Fetch total number of filtered programs for pagination
    const filteredProgramsCount = await Program.countDocuments(filter);

    // Fetch filtered programs with pagination
    const programs = await Program.find(filter)
      .sort({ createdAt: -1 }) // Sorting by newest first
      .skip(skip)
      .limit(limit)
      .lean();

    // Get base URL from env
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;

    // Fetch program statistics (Applicants & Beneficiaries)
    const programsSummary = await Promise.all(
      programs.map(async (program) => {
        const applicantsCount = await Applicant.countDocuments({ programId: program._id });
        const beneficiariesCount = await Applicant.countDocuments({
          programId: program._id,
          status: "selected", // Beneficiaries are applicants with status "selected"
        });

        return {
          _id: program._id,
          name: program.name,
          formLink: program.formId ? `${baseUrl}/forms/${program.linkName}` : null,
          status: program.status,
          startDate: program.startDate,
          endDate: program.endDate,
          applicantsCount,
          beneficiariesCount,
        };
      })
    );

   

    return NextResponse.json({
      programs: programsSummary,
      totalPrograms: totalProgramsCount,
      filteredPrograms: filteredProgramsCount,
      totalPages: Math.ceil(filteredProgramsCount / limit),
      currentPage: page,
    });
  } catch (error) {
    logger.error("Error fetching programs summary", {
      error: error.message,
      stack: error.stack
    });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

async function updateProgramStatuses(currentDate) {
  try {
    // Set date object to midnight to compare dates only (ignoring time)
    const normalizedCurrentDate = new Date(currentDate);
    normalizedCurrentDate.setHours(0, 0, 0, 0);

    // Update programs whose start date is in the future to 'upcoming'
    await Program.updateMany(
      {
        startDate: { $gt: normalizedCurrentDate },
        status: { $ne: "upcoming" },
      },
      { $set: { status: "upcoming" } }
    );

    // Update programs whose start date has arrived but end date hasn't passed to 'ongoing'
    await Program.updateMany(
      {
        startDate: { $lte: normalizedCurrentDate },
        endDate: { $gt: normalizedCurrentDate },
        status: { $ne: "ongoing" },
      },
      { $set: { status: "ongoing" } }
    );

    // Update programs whose end date has passed to 'completed'
    await Program.updateMany(
      {
        endDate: { $lte: normalizedCurrentDate },
        status: { $ne: "completed" },
      },
      { $set: { status: "completed" } }
    );
    
    ;
  } catch (error) {
    logger.error("Error updating program statuses by date", {
      error: error.message,
      stack: error.stack
    });
  }
}

// Export the secured GET handler with authentication
export const GET = withAuth(getProgramsSummaryHandler);