// components/profile/ErrorState.jsx
import { Button } from "@/components/ui/button"

export default function ErrorState({ retryFn }) {
  return (
    <div className="container flex items-center justify-center min-h-screen">
      <div className="text-center">
        <p className="text-red-500">Error loading profile data</p>
        <Button className="mt-4" onClick={retryFn}>
          Retry
        </Button>
      </div>
    </div>
  )
}