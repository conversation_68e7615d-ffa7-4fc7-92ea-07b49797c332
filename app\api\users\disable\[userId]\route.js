import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import User from "@/models/User";
import { withAdminAuth } from "@/middleware/authMiddleware";
import { rateLimit } from "@/lib/ratelimit";
import { createLogger } from "@/lib/logger";

// Create a custom logger for this route
const logger = createLogger({ service: "user-management" });

// Define the main handler function for disabling a user
async function disableUserHandler(req, { params }) {
  const { userId } = params;
  
  logger.info("Attempting to disable user", { userId });
  
  await dbConnect();

  try {
    const user = await User.findById(userId);
    
    if (!user) {
      logger.warn("User not found in disable attempt", { userId });
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }

    // Check if the user is an admin
    if (user.role === "admin") {
      logger.warn("Attempt to disable admin user prevented", { 
        userId,
        email: user.email
      });
      return NextResponse.json(
        { message: "Admin users cannot be disabled" }, 
        { status: 403 }
      );
    }
    
    user.status = "disabled";
    await user.save();
    
    logger.info("User disabled successfully", { 
      userId,
      email: user.email
    });

    return NextResponse.json(
      { message: "User disabled successfully", user },
      { status: 200 }
    );
  } catch (error) {
    logger.error("Failed to disable user", {
      userId,
      error: error.message,
      stack: error.stack
    });
    
    return NextResponse.json(
      { message: "Failed to disable user", error: error.message },
      { status: 500 }
    );
  }
}

// Apply rate limiting - 5 requests per minute
const disableUserRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // 5 requests per minute
  keyPrefix: "disable-user"
});

// Export the secured PATCH handler with rate limiting and admin auth
export async function PATCH(req, { params }) {
  // Apply rate limiting first
  const rateLimitResult = await disableUserRateLimit(req);
  if (!rateLimitResult.success) {
    logger.warn("Rate limit exceeded for user disable attempt", {
      ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown",
      path: req.nextUrl.pathname
    });
    return rateLimitResult.response;
  }
  
  // Then apply admin authentication
  return withAdminAuth(disableUserHandler)(req, { params });
}