"use client";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState, useEffect } from "react";
import { ThemeProvider } from "next-themes";
import AuthProvider from "@/components/SessionProvider";
import HydrationLoader from "@/components/HydrationLoader";
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Singleton pattern for QueryClient
let browserQueryClient = undefined;

function makeQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 15 * 60 * 1000, // 15 minutes
        retry: 1, // Only retry once
        refetchOnWindowFocus: false, // Prevent unnecessary refetches
        refetchOnReconnect: false, // Prevent refetch on reconnect
        refetchOnMount: false, // Prevent refetch on component mount
      },
    },
  });
}

function getQueryClient() {
  if (typeof window === 'undefined') {
    // Server: always make a new query client
    return makeQueryClient();
  } else {
    // Browser: make a new query client if we don't already have one
    if (!browserQueryClient) browserQueryClient = makeQueryClient();
    return browserQueryClient;
  }
}

export default function Providers({ children }) {
  const [mounted, setMounted] = useState(false);
  
  // Get the query client
  const queryClient = getQueryClient();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <HydrationLoader />;
  }

  return (
    <ThemeProvider attribute="class" defaultTheme="light">
      <QueryClientProvider client={queryClient}>
        <ReactQueryDevtools initialIsOpen={false} />
        <AuthProvider>{children}</AuthProvider>
      </QueryClientProvider>
    </ThemeProvider>
  );
}
