// app/profile/page.jsx
"use client"
import { useState } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { UserIcon, KeyIcon } from "lucide-react"
import DrawerLayout from "@/components/DrawerLayout"
import AuthOnly from "@/components/AuthOnly"
import { profileApi } from "@/services/profileApi"
import ProfileForm from "@/components/profile/ProfileForm"
import SecurityForm from "@/components/profile/SecurityForm"
import LoadingState from "@/components/profile/LoadingState"
import ErrorState from "@/components/profile/ErrorState"

export default function ProfilePage() {
  const { status } = useSession()
  const router = useRouter()
  const queryClient = useQueryClient()
  const [activeTab, setActiveTab] = useState("profile")

  // Redirect if not authenticated
  if (status === "unauthenticated") {
    router.push("/login")
  }

  // Fetch profile data with React Query
  const {
    data: profileData,
    isLoading,
    error: profileError,
  } = useQuery({
    queryKey: ["profile"],
    queryFn: profileApi.getProfile,
    enabled: status === "authenticated",
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  })

  if (isLoading || status === "loading") {
    return (
      <DrawerLayout>
        <LoadingState />
      </DrawerLayout>
    )
  }

  if (profileError) {
    return (
      <DrawerLayout>
        <ErrorState 
          retryFn={() => queryClient.invalidateQueries({ queryKey: ["profile"] })} 
        />
      </DrawerLayout>
    )
  }

  return (
    <AuthOnly>
      <DrawerLayout>
        <div className="min-h-screen p-4 text-white md:px-7">
          <div className="container">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="flex justify-center">
              <TabsList className="flex justify-center bg-card w-full max-w-md py-6 mb-4">
                
                <TabsTrigger value="profile" className="text-base py-5">
                  <UserIcon className="mr-2 h-4 w-4" />
                  Profile
                </TabsTrigger>
                <TabsTrigger value="security" className="text-base py-5">
                  <KeyIcon className="mr-2 h-4 w-4" />
                  Security
                </TabsTrigger>
              </TabsList>
              </div>
              <TabsContent value="profile" className="mt-2 space-y-8">
                <ProfileForm profileData={profileData} api={profileApi} />
              </TabsContent>

              <TabsContent value="security" className="mt-6">
                <SecurityForm api={profileApi} />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </DrawerLayout>
    </AuthOnly>
  )
}