import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import Applicant from "@/models/Applicant";
import { wards } from "@/lib/wards";

export async function GET(req, { params }) {
  try {
    await dbConnect();
    
    // Await params first (Next.js 15 requirement)
    const awaitedParams = await params;
    const { programId } = awaitedParams;

    // Get approved applicants for this program grouped by ward
    const applicants = await Applicant.find({ 
      programId, 
      status: "approved" 
    });

    // Group applicants by ward and count them
    const wardStats = {};
    let totalApplicants = 0;

    // Initialize all wards with 0 count
    wards.forEach(ward => {
      wardStats[ward] = {
        ward,
        count: 0,
        percentage: 0
      };
    });

    // Count applicants per ward
    applicants.forEach(applicant => {
      const ward = applicant.formData.get("ward");
      if (wards.includes(ward)) {
        wardStats[ward].count++;
        totalApplicants++;
      }
    });

    // Calculate percentages
    Object.values(wardStats).forEach(stat => {
      stat.percentage = totalApplicants > 0 
        ? Math.round((stat.count / totalApplicants) * 100 * 100) / 100 // Round to 2 decimal places
        : 0;
    });

    // Convert to array and sort by count (descending)
    const wardStatsArray = Object.values(wardStats).sort((a, b) => b.count - a.count);

    return NextResponse.json({
      success: true,
      data: {
        wardStats: wardStatsArray,
        totalApplicants,
        programId
      }
    });

  } catch (error) {
    console.error("Error fetching ward stats:", error);
    return NextResponse.json({ 
      success: false, 
      error: "Internal server error" 
    }, { status: 500 });
  }
}
