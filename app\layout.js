import { Inter } from "next/font/google"
import "./globals.css"
import Providers from "@/components/Providers"
const inter = Inter({ subsets: ["latin"] })
import { Toaster } from "@/components/ui/sonner"
import { GoogleAnalytics } from '@next/third-parties/google'

export const metadata = {
  title: "Maiwada Global | Haruna Maiwada Community Foundation",
  description: "Maiwada Global Foundation supporting community development and empowerment initiatives across Nigeria",
  keywords: "<PERSON>runa Mai<PERSON>, community foundation, maiwadaglobal, charity, community development",
  metadataBase: new URL('https://maiwadaglobal.com'),
  openGraph: {
    title: 'Haruna Maiwada Community Foundation',
    description: 'Supporting community development and empowerment initiatives',
    url: 'https://maiwadaglobal.com',
    siteName: 'Haruna Maiwada Community Foundation',
    locale: 'en_US',
    type: 'website',
  },
  alternates: {
    canonical: 'https://maiwadaglobal.com',
  },
}

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>{children}</Providers> 
        <Toaster position="top-center" richColors/>
        <GoogleAnalytics gaId="G-XXXXXXXXXX" />
      </body>
    </html>
  )
}





