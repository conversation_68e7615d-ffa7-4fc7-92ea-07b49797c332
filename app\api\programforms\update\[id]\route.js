import { NextResponse } from "next/server";
import ProgramForm from "@/models/ProgramForm";
import Program from "@/models/Program";
import Applicant from "@/models/Applicant";
import { dbConnect } from "@/lib/db";
import { z } from "zod";
import { withAdminAuth } from "@/middleware/authMiddleware";
import { rateLimit } from "@/lib/ratelimit";
import { sanitizeInput } from "@/lib/sanitize";
import { createLogger } from "@/lib/logger";

// Create a logger instance for this route
const logger = createLogger({ service: "program-form-api" });

// Zod schema for validation (all fields optional for partial updates)
const updateSchema = z.object({
  title: z.string().min(1, "Form title is required").optional(),
  description: z.string().optional(),
  programid: z.string().min(1, "Program ID is required").optional(),
  status: z.enum(["opened", "closed"]).optional(),
  fields: z.array(
    z.object({
      label: z.string().min(1, "Field label is required"),
      inputType: z.enum([
        "text",
        "email",
        "tel",
        "number",
        "date",
        "select",
        "textarea",
        "file",
      ]),
      required: z.boolean().default(false),
      options: z.array(z.string()).optional(),
    })
  )
  .optional()
  .refine(
    (fields) => {
      if (!fields) return true;
      const labels = fields.map(field => field.label);
      return labels.length === new Set(labels).size;
    },
    {
      message: "Duplicate field labels are not allowed",
      path: ["fields"],
    }
  ),
});

// Apply rate limit to protect against brute force attacks
const applyRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute window
  max: 10, // 10 requests per minute
  keyPrefix: "program-form-update" // Unique prefix for this endpoint
});

// Base handler function with all security features
async function handleProgramFormUpdate(req, { params }) {
  try {
    // Apply rate limiting
    const rateLimitResult = await applyRateLimit(req);
    if (!rateLimitResult.success) {
      logger.warn("Rate limit exceeded", {
        path: req.nextUrl.pathname,
        ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown"
      });
      return rateLimitResult.response;
    }

    await dbConnect(); // Connect to MongoDB

    logger.info("Processing program form update", {
      programId: params.id,
      user: req.user.email,
      method: req.method
    });

    // Extract program ID from URL params
    const { id } = params;
    
    // Get and sanitize the request body
    const rawBody = await req.json();
    const body = sanitizeInput(rawBody);
    
    logger.debug("Sanitized request body", { body });
    
    // Validate input with Zod
    const parsedData = updateSchema.parse(body);

    // Check if the program form exists
    let programForm = await ProgramForm.findOne({ programid: id });

    if (!programForm) {
      const programl = await Program.findById(id);
      if (!programl) {
        logger.warn("Program not found", { programId: id });
        return NextResponse.json({ error: "Program not found" }, { status: 404 });
      }

      logger.info("Creating new program form", { programId: id });
      
      // If program form doesn't exist, create a new one
      const newForm = new ProgramForm({
        title: parsedData.title || "New Program Form",
        programid: id,
        formLink: programl.linkName,
        status: parsedData.status || "opened",
        description: parsedData.description || "",
        fields: parsedData.fields || [],
      });

      programForm = await newForm.save();

      // Update the program with the new form's ID
      const program = await Program.findById(id);
      if (program) {
        program.formId = programForm._id;
        await program.save();
        logger.info("Program updated with new form ID", { 
          programId: id, 
          formId: programForm._id 
        });
      }

      return NextResponse.json(
        { message: "New program form created", form: programForm }, 
        { status: 201 }
      );
    }

    // If the form is being opened, check applicants and increment phase if needed
    if (parsedData.status === "opened") {
      const applicantCount = await Applicant.countDocuments({ programId: programForm.programid });

      if (applicantCount > 0) {
        parsedData.formPhase = (programForm.formPhase || 1) + 1;
        logger.info("Incrementing form phase", { 
          programId: id, 
          newPhase: parsedData.formPhase,
          applicantCount
        });
      } else if (!programForm.formPhase) {
        parsedData.formPhase = 1;
        logger.info("Initializing form phase", { programId: id });
      }
    }

    // Update the existing program form
    const updatedForm = await ProgramForm.findOneAndUpdate(
      { programid: id },
      parsedData,
      { new: true }
    );

    logger.info("Program form updated successfully", { 
      programId: id, 
      formId: updatedForm._id 
    });

    return NextResponse.json(
      { message: "Program form updated", form: updatedForm }, 
      { status: 200 }
    );
  } catch (error) {
    // Log the error with details
    logger.error("Error updating program form", {
      error: error.message,
      stack: error.stack,
      programId: params.id
    });

    if (error instanceof z.ZodError) {
      logger.warn("Validation error", { errors: error.errors });
      return NextResponse.json({ error: error.errors }, { status: 400 });
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// Export the PUT handler with admin authentication middleware
export const PUT = withAdminAuth(handleProgramFormUpdate);