const mongoose = require('mongoose');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/harunacommunity';

// Main function to add device fingerprint index
async function addDeviceFingerprintIndex() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully!');

    const db = mongoose.connection.db;
    const collection = db.collection('applicants');

    console.log('📊 Checking existing indexes...');
    const existingIndexes = await collection.indexes();
    console.log('Current indexes:', existingIndexes.map(idx => idx.name));

    // Check if the device fingerprint index already exists
    const deviceIndexExists = existingIndexes.some(idx => idx.name === 'unique_device_per_program');
    
    if (deviceIndexExists) {
      console.log('✅ Device fingerprint index already exists!');
    } else {
      console.log('🔧 Creating device fingerprint index...');
      
      // Create the compound unique index for programId and device fingerprint
      await collection.createIndex(
        {
          programId: 1,
          deviceFingerprint: 1
        },
        {
          unique: true,
          name: "unique_device_per_program",
          partialFilterExpression: {
            deviceFingerprint: { $exists: true }
          }
        }
      );
      
      console.log('✅ Device fingerprint index created successfully!');
    }

    // Verify the index was created
    const updatedIndexes = await collection.indexes();
    console.log('📊 Updated indexes:', updatedIndexes.map(idx => idx.name));

    // Check for any documents that might have duplicate device fingerprints
    console.log('🔍 Checking for potential device fingerprint duplicates...');
    
    const duplicates = await collection.aggregate([
      {
        $match: {
          deviceFingerprint: { $exists: true }
        }
      },
      {
        $group: {
          _id: {
            programId: "$programId",
            deviceFingerprint: "$deviceFingerprint"
          },
          docs: { $push: "$_id" },
          count: { $sum: 1 }
        }
      },
      {
        $match: {
          count: { $gt: 1 }
        }
      }
    ]).toArray();

    if (duplicates.length > 0) {
      console.log(`⚠️  Found ${duplicates.length} potential device fingerprint duplicates:`);
      duplicates.forEach((dup, index) => {
        console.log(`${index + 1}. Program: ${dup._id.programId}, Device: ${dup._id.deviceFingerprint}, Count: ${dup.count}`);
      });
      console.log('📝 You may need to manually review and clean these duplicates.');
    } else {
      console.log('✅ No device fingerprint duplicates found!');
    }

    // Get statistics
    const totalApplicants = await collection.countDocuments();
    const applicantsWithFingerprint = await collection.countDocuments({
      deviceFingerprint: { $exists: true }
    });

    console.log('\n📈 Statistics:');
    console.log(`Total applicants: ${totalApplicants}`);
    console.log(`Applicants with device fingerprint: ${applicantsWithFingerprint}`);
    console.log(`Applicants without device fingerprint: ${totalApplicants - applicantsWithFingerprint}`);

    console.log('\n✅ Device fingerprint index setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Error setting up device fingerprint index:', error);
    
    if (error.code === 11000) {
      console.error('💡 This appears to be a duplicate key error. You may have existing duplicates that need to be resolved first.');
    }
    
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed.');
  }
}

// Run the script
if (require.main === module) {
  addDeviceFingerprintIndex();
}

module.exports = { addDeviceFingerprintIndex };
