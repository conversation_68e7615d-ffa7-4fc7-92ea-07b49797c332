import { dbConnect } from "@/lib/db";
import { createLogger as createWinstonLogger, format, transports } from "winston";
import { MongoDB } from "winston-mongodb";

// Load MongoDB URI from environment
const mongoURI = process.env.MONGODB_URI;
if (!mongoURI) {
  throw new Error("MONGODB_URI is not defined in environment variables.");
}

// Format timestamp like "2025-04-21 12:37:50"
const readableTimestamp = format((info) => {
  const date = new Date(info.timestamp || Date.now());
  const pad = (n) => String(n).padStart(2, '0');
  const formatted =
    `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ` +
    `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
  info.timestamp = formatted;
  return info;
});

// Pretty JSON formatter for console
const prettyJsonFormat = format.printf(({ timestamp, level, message, metadata }) => {
  const logObject = {
    level,
    timestamp,
    msg: message,
    ...metadata,
  };
  return JSON.stringify(logObject, null, 2);
});

// MongoDB transport (no nested metadata)
const mongoTransport = new MongoDB({
  db: mongoURI,
  collection: "app_logs",
  level: "info",
  format: format.combine(
    format.timestamp(),
    readableTimestamp()
  )
});

// Base logger with shared formatting
const baseLogger = createWinstonLogger({
  level: "info",
  format: format.combine(
    format.errors({ stack: true }),
    format.timestamp(),
    readableTimestamp(),
    format.metadata({ fillExcept: ["message", "level", "timestamp", "label"] })
  ),
  transports: [
    new transports.Console({ format: prettyJsonFormat }),
    mongoTransport,
  ],
});

// Create child loggers with default meta
export const createLogger = (defaultMeta) => {
  return baseLogger.child({ ...defaultMeta });
};

export default baseLogger;
