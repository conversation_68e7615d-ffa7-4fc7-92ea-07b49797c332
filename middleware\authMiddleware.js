// middleware/authMiddleware.js
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { createLogger } from "@/lib/logger";

const logger = createLogger({ service: "auth-middleware" });

/**
 * Middleware to check if a user is authenticated
 * @param {Function} handler - The API route handler function
 * @returns {Function} - Wrapped handler with auth check
 */
export function withAuth(handler) {
  return async (req, { params }) => {
    try {
      // Get the session using Next Auth
      const session = await getServerSession(authOptions);
      
      if (!session || !session.user) {
        logger.warn("Unauthorized access attempt", { 
          path: req.nextUrl.pathname,
          ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown"
        });
        
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }
      
      // Add user info to request context
      req.session = session;
      req.user = session.user;
      
      
      // Continue to the handler
      return handler(req, { params });
    } catch (error) {
      logger.error("Authentication error", { 
        error: error.message,
        stack: error.stack,
        path: req.nextUrl.pathname
      });
      
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 500 }
      );
    }
  };
}

/**
 * Middleware to check if a user is authenticated and has admin role
 * @param {Function} handler - The API route handler function
 * @returns {Function} - Wrapped handler with auth and admin check
 */
export function withAdminAuth(handler) {
  return async (req, { params }) => {
    try {
      // Get the session using Next Auth
      const session = await getServerSession(authOptions);
      
      if (!session || !session.user) {
        logger.warn("Unauthorized access attempt to admin route", { 
          path: req.nextUrl.pathname,
          ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown"
        });
        
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }
      
      // Check if user has admin role
      if (!session.user.role || session.user.role !== "admin") {
        logger.warn("Non-admin access attempt to admin route", { 
          user: session.user.email,
          role: session.user.role || "undefined",
          path: req.nextUrl.pathname
        });
        
        return NextResponse.json(
          { error: "Admin access required" },
          { status: 403 }
        );
      }
      
      // Add user info to request context
      req.session = session;
      req.user = session.user;
      
     
      // Continue to the handler
      return handler(req, { params });
    } catch (error) {
      logger.error("Admin authentication error", { 
        error: error.message,
        stack: error.stack,
        path: req.nextUrl.pathname
      });
      
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 500 }
      );
    }
  };
}