"use client";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function ApplicantsFilters({
  search,
  setSearch,
  programId,
  setProgramId,
  status,
  setStatus,
  resetFilters,
  programsData,
  searchInputRef,
  programPage,
  programTotalPages,
  onProgramPageChange
}) {
  const programList = Array.isArray(programsData) ? programsData : [];

  

  return (
    <div className="bg-card border border-slate-700 rounded-xl p-6 mb-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <p className="mb-2 text-white text-sm">Search</p>
          <Input
            ref={searchInputRef}
            placeholder="Search by Name, Email, or Phone"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full bg-white text-white border border-slate-700 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-500 focus-visible:border-slate-500"
          />
        </div>  
        <div>
          <p className="mb-2 text-white text-sm">Filter by Program</p>
          <Select value={programId} onValueChange={setProgramId}>
            <SelectTrigger className="w-full bg-background text-accent border border-slate-700 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-500 focus-visible:border-slate-500">
              <SelectValue placeholder="Filter by Program" />
            </SelectTrigger>
            <SelectContent className="bg-card text-white border border-slate-700">
              <SelectItem value="all">All Programs</SelectItem>
              {programList.length > 0 ? (
                programList.map((program) => (
                  <SelectItem className="truncate" key={program._id} value={program._id}>
                    {program.name}
                  </SelectItem>
                ))
              ) : (
                <SelectItem disabled>No Programs Found</SelectItem>
              )}
              
              {/* Pagination Controls for Programs */}
              <div className="flex items-center justify-between border-t border-slate-700 mt-2 pt-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onProgramPageChange(programPage > 1 ? programPage - 1 : 1)}
                  disabled={programPage <= 1}
                  className="h-8 w-8 p-0 text-white"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-xs text-white">
                  {programPage} / {programTotalPages}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onProgramPageChange(programPage < programTotalPages ? programPage + 1 : programTotalPages)}
                  disabled={programPage >= programTotalPages}
                  className="h-8 w-8 p-0 text-white"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </SelectContent>
          </Select>
        </div>  
        <div>
          <p className="mb-2 text-white text-sm">Filter by Status</p>
          <Select value={status} onValueChange={setStatus}>
            <SelectTrigger className="w-full bg-background text-accent border border-slate-700 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-500 focus-visible:border-slate-500">
              <SelectValue placeholder="Filter by Status" />
            </SelectTrigger>
            <SelectContent className="bg-card text-white border border-slate-700">
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
              <SelectItem value="selected">Selected</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>  
      <div className="flex justify-end mt-4">
        <Button onClick={resetFilters} className="bg-blue-900 text-white dark:text-white font-bold hover:bg-blue-900">
          Reset Filters
        </Button>
      </div>
    </div>
  );
}