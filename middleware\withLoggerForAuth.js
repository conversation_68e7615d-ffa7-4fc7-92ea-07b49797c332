import { createLogger } from "@/lib/logger";

/**
 * Wraps NextAuth handler with a logger and attaches it to req.
 * This version works with NextAuth's custom authorize() function.
 */
export function withLoggerForAuth(handler, moduleName = "auth") {
  return async (req) => {
    try {
      const logger = createLogger({
        module: moduleName,
        method: req.method,
        path: req.url,
      });

      // Attach logger to the request so it’s accessible in authorize()
      req.logger = logger;

      return handler(req);
    } catch (err) {
      console.error("Logger middleware error:", err);
      return handler(req);
    }
  };
}
