"use client";
import { useSearchParams } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { z } from "zod";
import { toFormikValidationSchema } from "zod-formik-adapter";
import { <PERSON>, EyeOff, KeyRound, Loader2 } from "lucide-react";
import axios from "axios";

// Define password validation schema with Zod
const passwordSchema = z.object({
  newPassword: z.string()
    .min(6, "Password must be at least 6 characters")
    .max(100, "Password is too long")
});

export default function ResetPassword() {
  const [showPassword, setShowPassword] = useState(false);
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  if (!token) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="max-w-md mx-auto p-4 bg-card rounded-xl shadow-md text-center">
          <h2 className="text-xl font-bold mb-4 text-red-500">Invalid Reset Link</h2>
          <p>The password reset link is invalid or has expired.</p>
          <a href="/login" className="text-blue-500 hover:underline mt-4 inline-block">
            Return to login
          </a>
        </div>
      </div>
    );
  }

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      const response = await axios.post("/api/auth/reset-password", {
        token,
        newPassword: values.newPassword
      });
      
      toast.success(response.data.message || "Password reset successful");
      setTimeout(() => {
        window.location.href = "/";
      }, 2000);
    } catch (error) {
      const errorMessage = error.response?.data?.error || "Something went wrong";
      toast.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="h-screen flex items-center justify-center bg-background">
      <div className="max-w-md w-full mx-auto p-6 bg-card rounded-xl shadow-md">
        <div className="flex justify-center mb-6">
          <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
            <KeyRound className="h-6 w-6 text-green-600" />
          </div>
        </div>
        <h2 className="text-2xl font-bold mb-6 text-center">Reset Your Password</h2>
        
        <Formik
          initialValues={{ newPassword: "" }}
          validationSchema={toFormikValidationSchema(passwordSchema)}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting }) => (
            <Form className="space-y-4">
              <div className="form-control w-full">
                <label className="block text-sm font-medium mb-1">New Password</label>
                <div className="relative">
                  <Field
                    className="w-full p-3 text-black pr-10 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white bg-opacity-10 shadow-sm"
                    type={showPassword ? "text" : "password"}
                    name="newPassword"
                    placeholder="Enter your new password"
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                <ErrorMessage 
                  name="newPassword" 
                  component="div" 
                  className="text-red-500 text-sm mt-1" 
                />
                <p className="text-xs text-gray-500 mt-1">
                  Password must be at least 6 characters long
                </p>
              </div>

              <button
                className="w-full bg-green-600 hover:bg-green-700 text-white p-3 rounded-lg font-medium transition-colors flex items-center justify-center"
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" /> Resetting...
                  </>
                ) : (
                  "Reset Password"
                )}
              </button>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
}