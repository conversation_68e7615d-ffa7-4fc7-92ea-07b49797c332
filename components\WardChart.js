"use client"

import { useState, useEffect } from "react"
import axios from "axios"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useQuery } from "@tanstack/react-query"
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { ChevronDown, BarChart3, ChevronLeft, ChevronRight } from "lucide-react"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from "recharts"
import { Skeleton } from "@/components/ui/skeleton"

const statuses = ["all", "pending", "approved", "selected", "rejected"]

// Updated fetchPrograms function with pagination support
const fetchPrograms = async ({ page = 1, limit = 10 }) => {
  const res = await axios.get(`/api/programs/fetch?page=${page}&limit=${limit}`)
  return res.data // Return both programs and pagination info
}

const fetchWardStats = async ({ programId, status }) => {
  const query = new URLSearchParams()
  if (programId && programId !== "all") query.append("programId", programId)
  if (status && status !== "all") query.append("status", status)

  const response = await axios.get(`/api/applicants/ward-selection-stats?${query.toString()}`)
  return response.data.data || [] // Ensure we always return an array
}

// Enhanced dummy data for skeleton chart
const skeletonData = Array.from({ length: 10 }).map((_, i) => ({
  ward: `Ward ${i + 1}`,
  total: Math.floor(Math.random() * 20) + 10,
  selected: Math.floor(Math.random() * 10),
}))

export default function WardChart() {
  const [programId, setProgramId] = useState("all")
  const [selectedProgramName, setSelectedProgramName] = useState("All Programs")
  const [status, setStatus] = useState("all")
  const [selectedStatusLabel, setSelectedStatusLabel] = useState("All Statuses")
  // Pagination states
  const [currentPage, setCurrentPage] = useState(1)
  const [programsPerPage] = useState(8) // Show 8 programs per page in dropdown

  const [windowWidth, setWindowWidth] = useState(typeof window !== "undefined" ? window.innerWidth : 1024)

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Function to reset all filters to default values
  const resetFilters = () => {
    setProgramId("all")
    setSelectedProgramName("All Programs")
    setStatus("all")
    setSelectedStatusLabel("All Statuses")
    setCurrentPage(1)
  }

  // Fetch programs with proper error handling and pagination
  const { 
    data: programsResponse = { programs: [], pagination: { total: 0, page: 1, pages: 1 } }, 
    isLoading: programsLoading,
    isError: programsError
  } = useQuery({
    queryKey: ["programs", currentPage, programsPerPage],
    queryFn: () => fetchPrograms({ page: currentPage, limit: programsPerPage }),
  })

  // Extract programs and pagination info
  const programs = Array.isArray(programsResponse.programs) ? programsResponse.programs : []
  const pagination = programsResponse.pagination || { total: 0, page: 1, pages: 1 }

  const {
    data = [],
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["ward-stats", programId, status],
    queryFn: () => fetchWardStats({ programId, status }),
    refetchOnWindowFocus: false,
  })

  const chartData = isLoading ? skeletonData : data

  // Format data for better visualization
  const formattedData = chartData.map((item) => ({
    ...item,
    // Ensure ward names are consistently formatted
    ward: item.ward.startsWith("Ward") ? item.ward : `Ward ${item.ward}`,
  }))

  // Determine if we should show the "selected" bar based on status filter
  const showSelectedBar = status === "all"

  // Get the formatted status for labels
  const getFormattedStatus = () => {
    if (status === "all") return "Total"
    return status.charAt(0).toUpperCase() + status.slice(1)
  }

  // Dynamic labels based on current status filter
  const totalApplicantsLabel = `${getFormattedStatus()} Applicants`

  // Custom tooltip component with dynamic label
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-card p-4 border-2 border-slate-700 text-white rounded-md shadow-md">
          <p className="font-medium">{`Ward: ${label}`}</p>
          <p className="text-blue-600">{`${totalApplicantsLabel}: ${payload[0].value}`}</p>
          {payload[1] && <p className="text-lime-600">{`Selected Applicants: ${payload[1].value}`}</p>}
        </div>
      )
    }
    return null
  }

  return (
    <Card className="w-full py-5">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl flex flex-row items-center mb-2 justify-between text-white">Applicants Charts  <BarChart3 className="h-6 w-6 text-white" /></CardTitle>
            <CardDescription className="text-white">Distribution of applicants across different wards </CardDescription>
          </div>
          
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          {/* Program Dropdown */}
          <div className="w-full md:w-2/5">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between">
                  {selectedProgramName} <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56 bg-card max-h-60 overflow-y-auto text-white border border-slate-700">
                <DropdownMenuItem
                  onClick={() => {
                    setProgramId("all")
                    setSelectedProgramName("All Programs")
                  }}
                >
                  All Programs
                </DropdownMenuItem>
                {programsLoading ? (
                  <DropdownMenuItem disabled>Loading programs...</DropdownMenuItem>
                ) : programsError ? (
                  <DropdownMenuItem disabled>Error loading programs</DropdownMenuItem>
                ) : programs.length === 0 ? (
                  <DropdownMenuItem disabled>No programs available</DropdownMenuItem>
                ) : (
                  <>
                    {programs.map((program) => (
                      <DropdownMenuItem
                        key={program._id}
                        onClick={() => {
                          setProgramId(program._id)
                          setSelectedProgramName(program.name)
                        }}
                      >
                        {program.name}
                      </DropdownMenuItem>
                    ))}
                    
                    {/* Pagination Controls */}
                    {pagination.pages > 1 && (
                      <div className="flex justify-between items-center p-2 border-t border-slate-700 mt-2">
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                          disabled={currentPage === 1}
                          className="p-1"
                        >
                          <ChevronLeft className="h-4 w-4" />
                        </Button>
                        <span className="text-xs">
                          Page {pagination.page} of {pagination.pages}
                        </span>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => setCurrentPage(prev => Math.min(prev + 1, pagination.pages))}
                          disabled={currentPage === pagination.pages}
                          className="p-1"
                        >
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Status Dropdown */}
          <div className="w-full md:w-2/5">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between">
                  {selectedStatusLabel} <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56 bg-card text-white border border-slate-700">
                {statuses.map((s) => (
                  <DropdownMenuItem
                    key={s}
                    onClick={() => {
                      setStatus(s)
                      setSelectedStatusLabel(s === "all" ? "All Statuses" : s.charAt(0).toUpperCase() + s.slice(1))
                    }}
                  >
                    {s === "all" ? "All Statuses" : s.charAt(0).toUpperCase() + s.slice(1)}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Reset Filters Button */}
          <div className="w-full md:w-1/5">
            <Button
              variant="secondary"
              className="w-full bg-slate-700 hover:bg-slate-600 text-white"
              onClick={resetFilters}
            >
              Reset Filters
            </Button>
          </div>
        </div>

        <div className="h-96 w-full overflow-x-auto">
          {isError ? (
            <div className="flex flex-col items-center justify-center h-full">
              <p className="text-destructive font-medium">Error loading chart data</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => {
                  window.location.reload()
                }}
              >
                Try Again
              </Button>
            </div>
          ) : isLoading ? (
            <div className="space-y-2 w-full">
              <Skeleton className="h-96 w-full bg-muted" />
            </div>
          ) : formattedData.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full">
              <p className="text-muted-foreground">No data available for the selected filters</p>
            </div>
          ) : (
            <ResponsiveContainer
              width="100%"
              height="100%"
              minWidth={formattedData.length * (windowWidth < 640 ? 60 : 80)}
            >
              <BarChart data={formattedData} margin={{ top: 10, right: 30, left: 20, bottom: 90 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" vertical={false} />
                <XAxis
                  dataKey="ward"
                  tick={{ fontSize: 11 }}
                  interval={0}
                  angle={-45}
                  textAnchor="end"
                  tickMargin={12}
                  stroke="#f8fafc"
                />
                <YAxis stroke="#f8fafc" />
                <Tooltip content={<CustomTooltip />} />
                <Legend verticalAlign="top" height={36} wrapperStyle={{ paddingTop: "10px" }} />
                <Bar
                  dataKey="total"
                  name={totalApplicantsLabel}
                  fill="#2563eb"
                  radius={[4, 4, 0, 0]}
                  barSize={windowWidth < 640 ? 20 : 28}
                />
                {showSelectedBar && (
                  <Bar
                    dataKey="selected"
                    name="Selected Applicants"
                    fill="#34d399"
                    radius={[4, 4, 0, 0]}
                    barSize={windowWidth < 640 ? 20 : 28}
                  />
                )}
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  )
}