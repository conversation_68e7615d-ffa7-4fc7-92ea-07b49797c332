"use client";

import { useEffect, useState } from "react";
import axios from "axios";
import {
  PanelRightInactiveIcon,
  Calendar,
  Clock,
  Award,
  AlertCircle,
  Info,
  MapPin,
} from "lucide-react";

export default function ProgramDetailsCard({ programId }) {
  const [program, setProgram] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!programId) return;

    async function fetchProgram() {
      try {
        const response = await axios.get(`/api/programs/fetch/${programId}`);
        setProgram(response.data.program);
      } catch (err) {
        setError(
          err.response?.data?.error || "Failed to fetch program details."
        );
      } finally {
        setLoading(false);
      }
    }

    fetchProgram();
  }, [programId]);

  // Calculate countdown or status
  const getStatusDetails = () => {
    if (!program) return null;

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize to start of day

    const startDate = new Date(program.startDate);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(program.endDate);
    endDate.setHours(0, 0, 0, 0);

    if (program.status === "completed") {
      return {
        text: "Completed",
        icon: <Award className="h-4 w-4 text-green-400" />,
        color: "text-green-400",
      };
    } else if (program.status === "upcoming") {
      // Calculate days to start (including edge cases)
      const daysToStart = Math.floor(
        (startDate - today) / (1000 * 60 * 60 * 24)
      );

      if (daysToStart === 0) {
        return {
          text: "Starts today",
          icon: <Clock className="h-4 w-4 text-amber-400" />,
          color: "text-amber-400",
        };
      } else if (daysToStart === 1) {
        return {
          text: "Starts tomorrow",
          icon: <Clock className="h-4 w-4 text-amber-400" />,
          color: "text-amber-400",
        };
      } else {
        return {
          text: `Starts in ${daysToStart} days`,
          icon: <Clock className="h-4 w-4 text-amber-400" />,
          color: "text-amber-400",
        };
      }
    } else {
      // Must be ongoing
      const daysToEnd = Math.floor((endDate - today) / (1000 * 60 * 60 * 24));

      if (daysToEnd === 0) {
        return {
          text: "Ends today",
          icon: <Clock className="h-4 w-4 text-blue-400" />,
          color: "text-blue-400",
        };
      } else if (daysToEnd === 1) {
        return {
          text: "Ends tomorrow",
          icon: <Clock className="h-4 w-4 text-blue-400" />,
          color: "text-blue-400",
        };
      } else {
        return {
          text: `${daysToEnd} days remaining`,
          icon: <Clock className="h-4 w-4 text-blue-400" />,
          color: "text-blue-400",
        };
      }
    }
  };

  const getStatusBadge = () => {
    if (!program) return null;

    let badgeClass = "";
    let statusText = "";

    switch (program.status) {
      case "completed":
        badgeClass = "bg-green-500/10 text-green-500 border-green-500/20";
        statusText = "Completed";
        break;
      case "upcoming":
        badgeClass = "bg-amber-500/10 text-amber-500 border-amber-500/20";
        statusText = "Upcoming";
        break;
      default:
        badgeClass = "bg-blue-500/10 text-blue-500 border-blue-500/20";
        statusText = "Ongoing";
    }

    return (
      <span
        className={`text-xs font-medium px-2.5 py-0.5 rounded-full border ${badgeClass}`}
      >
        {statusText}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="w-11/12 md:max-w-3xl mx-auto mt-4 animate-fade-in">
        <div className="flex justify-center items-center gap-2 mb-4">
         
          <div className="h-12 bg-slate-700 rounded-lg w-full animate-pulse"></div>
        </div>

        <div className="bg-card border border-slate-700/50 rounded-xl overflow-hidden shadow-lg">
          <div className="h-2 bg-gradient-to-r from-primary/50 to-primary/10 animate-pulse"></div>

          <div className="p-5">
            {/* Description skeleton */}
            <div className="mb-6">
              <div className="h-4 bg-slate-700 rounded w-1/4 mb-3 animate-pulse"></div>
              <div className="h-3 bg-slate-700 rounded w-full mb-2 animate-pulse"></div>
              <div className="h-3 bg-slate-700 rounded w-5/6 mb-2 animate-pulse"></div>
              <div className="h-3 bg-slate-700 rounded w-4/6 animate-pulse"></div>
            </div>

            {/* Bento grid skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-slate-800/30 rounded-xl p-4 border border-slate-700/50">
                <div className="h-4 bg-slate-700 rounded w-1/3 mb-3 animate-pulse"></div>
                <div className="h-3 bg-slate-700 rounded w-1/2 animate-pulse"></div>
              </div>
              <div className="bg-slate-800/30 rounded-xl p-4 border border-slate-700/50">
                <div className="h-4 bg-slate-700 rounded w-1/3 mb-3 animate-pulse"></div>
                <div className="h-3 bg-slate-700 rounded w-1/2 animate-pulse"></div>
              </div>
              <div className="bg-slate-800/30 rounded-xl p-4 border border-slate-700/50">
                <div className="h-4 bg-slate-700 rounded w-1/3 mb-3 animate-pulse"></div>
                <div className="h-3 bg-slate-700 rounded w-1/2 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-11/12 md:max-w-3xl mx-auto mt-8 animate-fade-in">
        <div className="bg-card border border-red-500/20 rounded-xl p-4 flex items-center gap-3">
          <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
          <p className="text-red-500">{error}</p>
        </div>
      </div>
    );
  }

  const statusDetails = getStatusDetails();

  return (
    <div className=" md:max-w-3xl mx-auto mt-4 animate-fade-in">
      {/* Title with badge */}

      {/* Main card */}
      <div className="bg-card border border-slate-700/50 rounded-xl overflow-hidden shadow-lg transition-all duration-300 hover:shadow-primary/5">
        <div className=""></div>
        <div className="flex flex-col   justify-center items-center gap-2 mb-4">
          <div className=" overflow-hidden   bg-slate-700/50 dark:bg-slate-900  m-6  py-6 w-11/12  items-center justify-center flex flex-row rounded-2xl">
            <div className="flex justify-center max-w-7/12 md:max-w-10/12  items-center ">
              <h2 className="   text-sm md:text-xl break-words  line-clamp-3  font-bold">
                {program.name}
              </h2>
            </div>
            <div className=" flex ">{getStatusBadge()}</div>
          </div>
        </div>
        <div className="p-5">
          {/* Description */}
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-3">
              <Info className="h-4 w-4 " />
              <h3 className="text-sm font-medium  uppercase tracking-wider">
                Program Description
              </h3>
            </div>
            <p className="text-base break-words  line-clamp-3 leading-relaxed">
              {program.description}
            </p>
          </div>

          {/* Bento grid layout */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Start Date */}
            <div className="bg-slate-800/30 dark:bg-slate-900  backdrop-blur-sm rounded-xl p-4 border border-slate-700/50 hover:bg-slate-800/50 transition-all duration-300">
              <div className="flex items-center mb-2">
                <Calendar size={16} className="mr-2 text-amber-400" />
                <h3 className="text-sm font-medium uppercase tracking-wider">
                  Start Date
                </h3>
              </div>
              <p className="text-lg font-medium">
                {new Date(program.startDate).toLocaleDateString(undefined, {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                })}
              </p>
            </div>

            {/* End Date */}
            <div className="bg-slate-800/30 dark:bg-slate-900  backdrop-blur-sm rounded-xl p-4 border border-slate-700/50 hover:bg-slate-800/50 transition-all duration-300">
              <div className="flex items-center  mb-2">
                <Calendar size={16} className="mr-2 text-blue-400" />
                <h3 className="text-sm font-medium uppercase tracking-wider">
                  End Date
                </h3>
              </div>
              <p className="text-lg font-medium">
                {new Date(program.endDate).toLocaleDateString(undefined, {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                })}
              </p>
            </div>

            {/* Status */}
            <div className="bg-slate-800/30 dark:bg-slate-900  backdrop-blur-sm rounded-xl p-4 border border-slate-700/50 hover:bg-slate-800/50 transition-all duration-300">
              <div className="flex items-center  mb-2">
                <MapPin size={16} className="mr-2 text-emerald-600" />
                <h3 className="text-sm font-medium uppercase tracking-wider">
                  Venue
                </h3>
              </div>
              <div className="flex items-center gap-2">
                
                <p className="text-sm  break-words line-clamp-3 font-medium">
                {program.venue}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
