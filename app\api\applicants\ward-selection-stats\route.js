import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import Applicant from "@/models/Applicant";
import { wards } from "@/lib/wards";

export async function GET(req) {
  await dbConnect();

  const { searchParams } = new URL(req.url);
  const programId = searchParams.get("programId");
  const statusFilter = searchParams.get("status");

  const matchBase = {};
  if (programId) matchBase.programId = programId;

  const statusIsFiltered = statusFilter && statusFilter !== "all";

  try {
    // Total applicants per ward (respect status filter if applied)
    const totalMatch = {
      ...matchBase,
      ...(statusIsFiltered && { status: statusFilter }),
    };

    const totals = await Applicant.aggregate([
      { $match: totalMatch },
      {
        $group: {
          _id: "$formData.ward",
          total: { $sum: 1 },
        },
      },
    ]);

    // Selected applicants per ward (always status = "selected")
    const selectedMatch = {
      ...matchBase,
      status: "selected",
    };

    const selected = await Applicant.aggregate([
      { $match: selectedMatch },
      {
        $group: {
          _id: "$formData.ward",
          selected: { $sum: 1 },
        },
      },
    ]);

    // Combine results
    const totalsMap = Object.fromEntries(totals.map(({ _id, total }) => [_id, total]));
    const selectedMap = Object.fromEntries(selected.map(({ _id, selected }) => [_id, selected]));

    const result = wards.map((ward) => ({
      ward,
      total: totalsMap[ward] || 0,
      selected: selectedMap[ward] || 0,
    }));

    return NextResponse.json({ success: true, data: result });
  } catch (err) {
    console.error(err);
    return NextResponse.json({ success: false, error: "Server error" }, { status: 500 });
  }
}
