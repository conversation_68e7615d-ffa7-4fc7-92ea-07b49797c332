import { useState, useEffect, useRef } from "react"
import { useQuery } from "@tanstack/react-query"
import axios from "axios"
import { useRouter } from "next/navigation"
import { Users, UserCheck, Link as LinkIcon, Loader2, MoreHorizontal, Search } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Pagination } from "@/components/Pagination"
import Pskeleton from "./Pskeleton"
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel } from "@/components/ui/dropdown-menu"
import { SearchAndFilter } from "@/components/SearchAndFilter"
import CountUp from "react-countup";

export default function ProgramsCard() {
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState("")
  const [status, setStatus] = useState("all")
  const [debouncedSearch, setDebouncedSearch] = useState("")
  const [isDebouncing, setIsDebouncing] = useState(false)
  const limit = 6
  const router = useRouter()
  const searchInputRef = useRef(null)

  useEffect(() => {
    setIsDebouncing(true)
    const timeout = setTimeout(() => {
      setDebouncedSearch(search)
      setIsDebouncing(false)
    }, 500)
    return () => clearTimeout(timeout)
  }, [search])

  const { data, isFetching, isLoading, isError, error } = useQuery({
    queryKey: ["programsSummary", page, debouncedSearch, status],
    queryFn: async () => {
      const apiStatus = status === "all" ? "" : status
      const response = await axios.get(
        `/api/programs/summary?page=${page}&limit=${limit}&search=${debouncedSearch}&status=${apiStatus}`
      )
      return response.data
    },
    keepPreviousData: true,
    staleTime: 5 * 60 * 1000,
  })

  useEffect(() => {
    if (searchInputRef.current && document.activeElement === searchInputRef.current) {
      const currentValue = searchInputRef.current.value
      searchInputRef.current.focus()
      searchInputRef.current.setSelectionRange(currentValue.length, currentValue.length)
    }
  }, [data?.programs])

  return (
    <div className="p-4 w-full relative">
      {/* Loading overlay (only shows during fetching, not debouncing) */}
      {isFetching && !isDebouncing && (
        <div className="absolute inset-0  z-10 flex items-center justify-center">
       
        </div>
      )}

      {/* Stats Summary - Always shown */}
      <div className="mb-4 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-card border border-slate-700 p-4 rounded-lg text-white">
          <h3 className="text-sm font-medium">Programs </h3>
          <p className="text-2xl font-bold"> Stats</p>
        </div>
        
        <div className="bg-card border border-slate-700 p-4 rounded-lg text-white">
          <h3 className="text-sm font-medium ">Total Programs </h3>
          <p className="text-2xl font-bold">{data?.totalPrograms || 0}</p>
        </div>
        
        <div className="bg-card border border-slate-700 p-4 rounded-lg text-white">
          <h3 className="text-sm font-medium ">Showing Result</h3>
          <p className="text-2xl font-bold"> 
            {data ? <CountUp end={data.filteredPrograms || 0} duration={2} separator="," /> : 0}
          </p>
        </div>
      </div>
      
      {/* Content with smooth opacity transition */}
      <div className={`transition-opacity duration-200 ${isDebouncing ? 'opacity-50' : 'opacity-100'}`}>
        <SearchAndFilter
          search={search}
          setSearch={setSearch}
          status={status}
          setStatus={setStatus}
          searchInputRef={searchInputRef}
        />

        {/* Show skeleton when loading initial data */}
        {isLoading && !search ? (
          <Pskeleton />
        ) : isError ? (
          <div className="text-center text-red-500">{error.message || "Failed to fetch programs"}</div>
        ) : data?.programs?.length === 0 ? (
          <div className="text-center flex flex-col justify-center items-center py-16">
            <Search size={100} className="text-slate-500 mb-4" />
            <p className="text-lg text-slate-500">No programs to display</p>
          </div>
        ) : (
          <div className="grid card w-full gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {data?.programs?.map((program) => (
              <div key={program._id} className="relative bg-card w-full border border-slate-700 p-4 rounded-2xl">
                <div className="absolute top-2">
                  <Badge
                    variant="outline"
                    className={`px-2 py-1 text-xs ${
                      program.status === "ongoing" ? "border  border-lime-600 text-lime-600" :
                      program.status === "upcoming" ? "border border-amber-500 text-amber-500" :
                      "border border-blue-700 text-blue-700"
                    }`}
                  >
                    {program.status}
                  </Badge>
                </div>

                <div className="card-body  text-white">
                  <span className="text-sm font-semibold truncate text-center mb-2">{program.name}</span>
                  <div className="divider w-full -mt-3"/>
                  <div className="flex items-center gap-2 -mt-3 mb-2">
                    <Users size={20} className="text-green-500" />
                    <span className="text-sm">Applicants: {program.applicantsCount}</span>
                  </div>
                  <div className="flex items-center gap-2 mb-2">
                    <UserCheck size={20} className="text-indigo-500" />
                    <span className="text-sm">Beneficiaries: {program.beneficiariesCount}</span>
                  </div>

                  <div className="flex items-center gap-2">
                    <LinkIcon size={20} className="text-blue-500" />
                    {program.formLink ? (
                      <a href={program.formLink} target="_blank" rel="noopener noreferrer" className="text-blue-500 underline">
                        View Form
                      </a>
                    ) : (
                      <span className="text-gray-400">No Form yet</span>
                    )}
                  </div>

                  <div className="flex text-sm text-gray-300 mb-3 mt-2">
                    <span className="p-1 rounded-lg text-center border border-slate-700">📅 Starts: {program.startDate ? new Date(program.startDate).toLocaleDateString() : ''}</span>
                    <span className="p-1 rounded-lg text-center border border-slate-700">⏳ Ends: {program.endDate ? new Date(program.endDate).toLocaleDateString() : ''}</span>
                  </div>

                  <div className="absolute bottom-2 right-5">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <button className="btn h-8 text-accent text-xs mb-0.5 font-light border-2 border-slate-700 rounded-lg bg-background hover:bg-blue-900 flex items-center gap-0">
                          <MoreHorizontal size={24} />
                        </button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-card text-white border border-slate-700 rounded-lg">
                        <DropdownMenuItem onClick={() => router.push(`programs/programdetails/${program._id}`)}>Full Program Details</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        
        {data?.programs?.length >= 1 ? (
          <Pagination
            page={page} 
            totalPages={data?.totalPages} 
            setPage={setPage} 
          />
        ) : null}
      </div>
    </div>
  )
}