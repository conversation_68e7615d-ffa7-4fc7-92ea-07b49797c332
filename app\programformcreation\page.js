"use client";
import DrawerLayout from "@/components/DrawerLayout";
import { useState } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { Plus } from "lucide-react";
import { z } from "zod";
import { toFormikValidationSchema } from "zod-formik-adapter";
import axios from "axios";
import { toast } from "sonner";
import FieldComponent from "@/components/FieldComponent";
import { wards } from "@/lib/wards";
import ProgramSelect from "@/components/ProgramSelect";

const inputTypes = ["text", "email", "tel", "number", "date", "textarea", "file", "select"];

const defaultFields = [
  { label: "Name", inputType: "text", required: true, isDefault: true },
  { label: "Gender", inputType: "select", required: true, isDefault: true, options: ["Male", "Female"] },
  { label: "Email", inputType: "email", required: true, isDefault: true },
  { label: "PhoneNumber", inputType: "tel", required: true, isDefault: true },
  { label: "State", inputType: "select", required: true, isDefault: true, options: ["Katsina"] },
  { label: "Ward", inputType: "select", required: true, isDefault: true, options: wards },
];

export default function DynamicFormBuilder() {
  const [fields, setFields] = useState(defaultFields);

  // Validate the whole form for duplicate labels
  const validateForm = (values) => {
    const errors = {};
    
    // Check for duplicate labels
    const labelMap = new Map();
    values.fields.forEach((field, index) => {
      const label = field.label.trim();
      if (label) {
        if (labelMap.has(label)) {
          if (!errors.fields) {
            errors.fields = "Duplicate field labels found. Each label must be unique.";
          }
        } else {
          labelMap.set(label, index);
        }
      }
    });
    
    return errors;
  };

  const addField = (setFieldValue) => {
    // Create a unique label for the new field
    let newLabel = "Field";
    let counter = 1;
    let labelExists = true;
    
    while (labelExists) {
      const candidateLabel = `${newLabel} ${counter}`;
      if (!fields.some(field => field.label === candidateLabel)) {
        newLabel = candidateLabel;
        labelExists = false;
      } else {
        counter++;
      }
    }
    
    const newFields = [...fields, { 
      label: newLabel, 
      inputType: "text", 
      required: false, 
      isDefault: false, 
      options: [] 
    }];
    
    setFields(newFields);
    setFieldValue("fields", newFields);
  };

  const removeField = (index, setFieldValue) => {
    if (fields[index].isDefault) return;
    const newFields = fields.filter((_, i) => i !== index);
    setFields(newFields);
    setFieldValue("fields", newFields);
  };

  // Check if any field has validation errors
  const hasFieldErrors = (fields) => {
    return fields.some(field => field.error);
  };

  return (
    <DrawerLayout>
      <div className="min-h-screen text-white p-4">
        <div className="w-full md:max-w-2xl mx-auto my-10 p-6 border border-slate-800 bg-card shadow-lg rounded-lg">
          <h1 className="text-2xl font-bold text-center mb-4">Create New Form</h1>

          <Formik
            initialValues={{
              title: "",
              description: "",
              programid: "",
              fields: fields,
            }}
            validationSchema={toFormikValidationSchema(z.object({
              title: z.string().min(1, "Title is required"),
              description: z.string().optional(),
              programid: z.string().min(1, "Program selection is required"),
              fields: z
                .array(
                  z.object({
                    label: z.string().min(1, "Field label is required"),
                    inputType: z.enum(inputTypes),
                    required: z.boolean(),
                    isDefault: z.boolean().optional(),
                    options: z.array(z.string()).optional(),
                    error: z.string().optional(), // Add error field to schema
                  })
                )
                .min(1, "At least one field is required")
            }))}
            validate={validateForm}
            onSubmit={async (values, { setSubmitting, resetForm }) => {
              try {
                if (!values.title || !values.programid) {
                  toast.error("Please fill all required fields!");
                  setSubmitting(false);
                  return;
                }

                // Check for duplicate labels
                const labels = values.fields.map(f => f.label.trim());
                const uniqueLabels = new Set(labels);
                
                if (uniqueLabels.size !== labels.length) {
                  toast.error("Each field must have a unique label");
                  setSubmitting(false);
                  return;
                }

                // Check for field-level errors
                if (hasFieldErrors(values.fields)) {
                  toast.error("Please fix all field errors before submitting");
                  setSubmitting(false);
                  return;
                }

                // Remove any error properties before submitting
                const cleanedFields = values.fields.map(field => {
                  const { error, ...cleanField } = field;
                  return cleanField;
                });
                
                const dataToSubmit = {
                  ...values,
                  fields: cleanedFields
                };

                console.log("Submitting Form Data:", dataToSubmit);

                const response = await axios.post("/api/programforms/create", dataToSubmit);

                if (response.status === 201) {
                  toast.success("Form created successfully! 🎉");
                  resetForm();
                  setFields(defaultFields); // Reset custom fields
                } else {
                  toast.error("Failed to create form. Please try again.");
                }
              } catch (error) {
                console.error("Error creating form:", error);
                toast.error("Something went wrong. Please try again.");
              }
              setSubmitting(false);
            }}
          >
            {({ values, setFieldValue, isSubmitting, errors, touched }) => (
              <Form className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Form Title</span>
                    </label>
                    <Field type="text" name="title" className="input input-bordered text-accent bg-background w-full" />
                    <ErrorMessage name="title" component="p" className="text-red-500 text-sm" />
                  </div>

                  <div className="form-control ">
                    <label className="label">
                      <span className="label-text font-medium">Select Program</span>
                    </label>
                    <ProgramSelect onSelect={(selectedProgramId) => setFieldValue("programid", selectedProgramId)} />
                    <ErrorMessage name="programid" component="p" className="text-red-500 text-sm" />
                  </div>
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">Description</span>
                  </label>
                  <Field as="textarea" name="description" className="textarea bg-background text-accent textarea-bordered w-full" />
                </div>

                <h2 className="text-lg font-semibold">Fields</h2>
                
                {/* Form-level error for duplicate fields */}
                {errors.fields && typeof errors.fields === 'string' && (
                  <div className="alert alert-error text-sm p-2">
                    {errors.fields}
                  </div>
                )}
                
                <div className="grid md:grid-cols-2 grid-cols-1 gap-4 mb-4">
                  {values.fields?.map((field, index) => (
                    <FieldComponent
                      key={index}
                      field={field}
                      index={index}
                      fields={values.fields}
                      setFields={(updatedFields) => {
                        setFields(updatedFields);
                        setFieldValue("fields", updatedFields);
                      }}
                      removeField={() => removeField(index, setFieldValue)}
                      inputTypes={inputTypes}
                    />
                  ))}
                </div>

                <button
                  type="button"
                  onClick={() => addField(setFieldValue)}
                  className="btn btn-outline flex items-center gap-2 w-full"
                >
                  <Plus size={18} /> Add Field
                </button>

                <button 
                  type="submit" 
                  className="btn btn-primary w-full mt-4" 
                  disabled={isSubmitting || hasFieldErrors(values.fields)}
                >
                  {isSubmitting ? "Creating form..." : "Submit"}
                </button>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </DrawerLayout>
  );
}