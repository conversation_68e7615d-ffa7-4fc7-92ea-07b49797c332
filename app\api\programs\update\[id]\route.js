import { NextResponse } from "next/server";
import mongoose from "mongoose";
import slugify from "slugify";
import Program from "@/models/Program";
import ProgramForm from "@/models/ProgramForm";
import { dbConnect } from "@/lib/db";
import { z } from "zod";
import { withAdminAuth } from "@/middleware/authMiddleware";
import { rateLimit } from "@/lib/ratelimit";
import { createLogger } from "@/lib/logger";
import { sanitizeInput } from "@/lib/sanitize";

// Create a custom logger for this route
const logger = createLogger({ service: "program-update" });

// Define Zod schema for validation
const updateProgramSchema = z.object({
  name: z.string()
    .min(3, "Name must be at least 3 characters long")
    .max(40, "Program name cannot exceed 40 characters")
    .optional(),
  description: z.string().min(10, "Description must be at least 10 characters").optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  status: z.enum(["upcoming", "ongoing", "completed"]).optional(),
  maxBeneficiaries: z.number().int().positive("Max beneficiaries must be a positive integer").optional(),
  formId: z.string().optional().nullable(),
  venue: z.string().min(2, "Venue must be at least 2 characters long").max(100, "Venue cannot exceed 100 characters").optional(),
});

// Define the main handler function
async function updateProgramHandler(req, { params }) {
  try {
    await dbConnect();
  
    // Sanitize params
    const sanitizedParams = sanitizeInput(params);
    const { id } = sanitizedParams;

    if (!id) {
      logger.warn("Missing program ID in request", { params });
      return NextResponse.json({ error: "Program ID is required" }, { status: 400 });
    }

    // Parse and sanitize request body
    const rawBody = await req.json();
    const sanitizedBody = sanitizeInput(rawBody);
    
    logger.info("Processing program update request", { 
      programId: id, 
      requestFields: Object.keys(sanitizedBody) 
    });

    // Validate request body with Zod
    const parsedData = updateProgramSchema.safeParse(sanitizedBody);
    if (!parsedData.success) {
      logger.warn("Validation failed for program update", { 
        programId: id, 
        validationErrors: parsedData.error.format() 
      });
      return NextResponse.json({ error: parsedData.error.format() }, { status: 400 });
    }

    const updateData = parsedData.data;

    // Find the existing program
    const existingProgram = await Program.findById(id);
    if (!existingProgram) {
      logger.warn("Program not found for update", { programId: id });
      return NextResponse.json({ error: "Program not found" }, { status: 404 });
    }

    // Prevent updating if program is completed
    if (existingProgram.status === "completed") {
      logger.warn("Attempted to update a completed program", { 
        programId: id, 
        programName: existingProgram.name 
      });
      return NextResponse.json(
        { error: "Cannot update a program that has been completed." },
        { status: 400 }
      );
    }

    // If updating name, check for duplicates
    if (updateData.name && updateData.name !== existingProgram.name) {
      logger.info("Program name change detected", { 
        programId: id, 
        oldName: existingProgram.name, 
        newName: updateData.name 
      });
      
      const duplicateProgram = await Program.findOne({
        name: new RegExp(`^${updateData.name}$`, "i"),
        _id: { $ne: id },
      });
      
      if (duplicateProgram) {
        logger.warn("Duplicate program name detected", { 
          programId: id, 
          duplicateName: updateData.name 
        });
        return NextResponse.json({ error: "A program with this name already exists" }, { status: 409 });
      }

      // Generate new linkName
      updateData.linkName = slugify(updateData.name, { lower: true, strict: true });
      logger.info("Generated new link name", { 
        programId: id, 
        newLinkName: updateData.linkName 
      });

      // Update the form's formLink if it exists
      const form = await ProgramForm.findOne({ programid: id });
      if (form) {
        const newFormLink = `http://localhost:3000/forms/${updateData.linkName}`;
        await ProgramForm.findByIdAndUpdate(form._id, { formLink: newFormLink });
        logger.info("Updated associated form link", { 
          programId: id, 
          formId: form._id, 
          newFormLink 
        });
      }
    }

    // Log venue update if provided
    if (updateData.venue) {
      logger.info("Updating program venue", {
        programId: id,
        oldVenue: existingProgram.venue || "None",
        newVenue: updateData.venue
      });
    }

    // Perform the update
    const updatedProgram = await Program.findByIdAndUpdate(id, updateData, { new: true });
    
    logger.info("Program updated successfully", { 
      programId: id, 
      updatedFields: Object.keys(updateData) 
    });

    // Sanitize the response data
    const sanitizedProgram = sanitizeInput(updatedProgram.toObject());

    return NextResponse.json(
      { message: "Program updated successfully", program: sanitizedProgram },
      { status: 200 }
    );
  } catch (error) {
    logger.error("Error updating program", {
      error: error.message,
      stack: error.stack
    });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// Apply rate limiting - 10 requests per minute
const updateProgramRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 requests per minute
  keyPrefix: "program-update"
});

// Export the secured PUT handler with rate limiting and admin auth
export async function PUT(req, { params }) {
  // Apply rate limiting first
  const rateLimitResult = await updateProgramRateLimit(req);
  if (!rateLimitResult.success) {
    logger.warn("Rate limit exceeded for program update", {
      path: req.nextUrl.pathname,
      ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown"
    });
    return rateLimitResult.response;
  }
  
  // Then apply admin authentication
  return withAdminAuth(updateProgramHandler)(req, { params });
}