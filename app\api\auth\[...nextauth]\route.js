import NextAuth from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import { dbConnect } from "@/lib/db";
import User from "@/models/User";
import bcrypt from "bcryptjs";
import { createLogger } from "@/lib/logger";
import { rateLimit } from "@/lib/ratelimit";
import { z } from "zod";
import mongoose from "mongoose";

// Create a schema for credentials validation
const credentialsSchema = z.object({
  username: z.string()
    .min(1, "Username is required")
    .max(254, "Username is too long")
    // Updated regex to allow email format while preventing NoSQL injection
    // This accepts alphanumeric, underscore, dot, hyphen, plus sign, and @ symbol
    .regex(/^[a-zA-Z0-9_.+-]+@?[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/, "Username contains invalid characters"),
  password: z.string()
    .min(1, "Password is required")
});

const loginRateLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5,
  keyPrefix: "login",
});

const logger = createLogger({ service: "auth" });

export const authOptions = {
  session: {
    strategy: "jwt",
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: {
          label: "Username",
          type: "text",
          placeholder: "yourusername",
        },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials, req) {
        // Validate credentials with Zod
        try {
          const validatedCredentials = credentialsSchema.parse(credentials);
        } catch (validationError) {
          logger.warn("Validation error", {
            error: validationError.errors,
          });
          throw new Error("Invalid input data");
        }

        const forwarded = req?.headers?.["x-forwarded-for"];
        const ip =
          typeof forwarded === "string"
            ? forwarded.split(",")[0].trim()
            : req?.socket?.remoteAddress || "unknown";

        const rate = await loginRateLimiter({
          headers: new Headers({ "x-forwarded-for": ip }),
        });

        if (!rate.success) {
          logger.warn("Rate limit exceeded", {
            username: credentials.username,
            ip,
          });
          const err = new Error("rate-limit");
          err.name = "RateLimitError";
          throw err;
        }

        try {
          await dbConnect();
        } catch (dbError) {
          logger.error("Database connection failed", {
            error: dbError.message,
            stack: dbError.stack,
            username: credentials.username,
            ip
          });
          const err = new Error("database-error");
          err.name = "DatabaseConnectionError";
          throw err;
        }
        
        // Use validated username from Zod schema
        const { username } = credentials;

        try {
          // Use a sanitized username query to prevent NoSQL injection
          // MongoDB query is now safe because username has been validated
          const user = await User.findOne({ username });

          if (!user) {
            logger.warn("Login failed: User not found", { username });
            throw new Error("Invalid username or password");
          }

          if (user.status === "disabled") {
            logger.warn("Login attempt for disabled user", {
              username,
              userId: user._id.toString(),
            });
            throw new Error("disabled");
          }

          const isMatch = await bcrypt.compare(
            credentials.password,
            user.password
          );
          if (!isMatch) {
            logger.warn("Login failed: Incorrect password", {
              username,
              userId: user._id.toString(),
            });
            throw new Error("Invalid username or password");
          }

          logger.info("User logged in successfully", {
            username,
            userId: user._id.toString(),
          });

          return {
            id: user._id.toString(),
            name: user.name,
            username: user.username,
            role: user.role,
          };
        } catch (error) {
          // Check if this is a MongoDB connection error
          if (error instanceof mongoose.Error.MongooseServerSelectionError) {
            logger.error("MongoDB server selection error", {
              username,
              error: error.message,
              stack: error.stack
            });
            const err = new Error("database-error");
            err.name = "DatabaseConnectionError";
            throw err;
          }
          
          logger.error("Login error", {
            username,
            error: error.message,
          });
          throw error;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        return { ...token, ...user };
      }
      return token;
    },
    async session({ session, token }) {
      session.user = token;
      return session;
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };