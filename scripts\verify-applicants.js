const mongoose = require('mongoose');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/haruna';

// Applicant Schema (matching your model)
const ApplicantSchema = new mongoose.Schema(
  {
    programId: {
      type: String,
      required: true,
    },
    formPhase: {
      type: Number,
      required: true,
    },
    formData: {
      type: Map,
      of: mongoose.Schema.Types.Mixed,
      required: true,
    },
    status: {
      type: String,
      enum: ["pending", "approved", "rejected", "selected"],
      default: "pending",
    },
    reviewerId: {
      type: String,
      default: null,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  { timestamps: true }
);

const Applicant = mongoose.models.Applicant || mongoose.model('Applicant', ApplicantSchema);

// Main function to verify applicants
async function verifyApplicants() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully!');

    // Count total applicants
    const totalCount = await Applicant.countDocuments();
    console.log(`📊 Total applicants in database: ${totalCount}`);

    // Count by specific program ID
    const programId = '680acb7b1d550cd5912788cb';
    const programCount = await Applicant.countDocuments({ programId });
    console.log(`📊 Applicants for program ${programId}: ${programCount}`);

    // Count by status
    const statusCounts = await Applicant.aggregate([
      { $group: { _id: "$status", count: { $sum: 1 } } }
    ]);
    console.log('📊 Status distribution:');
    statusCounts.forEach(({ _id, count }) => {
      console.log(`  ${_id}: ${count}`);
    });

    // Count by ward for the specific program
    const wardCounts = await Applicant.aggregate([
      { $match: { programId } },
      { $group: { _id: "$formData.ward", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    console.log(`📊 Ward distribution for program ${programId}:`);
    wardCounts.forEach(({ _id, count }) => {
      console.log(`  ${_id}: ${count}`);
    });

    // Show a few sample applicants
    const samples = await Applicant.find({ programId }).limit(3);
    console.log('\n📋 Sample applicants:');
    samples.forEach((applicant, index) => {
      console.log(`${index + 1}. Name: ${applicant.formData.get('name')}, Ward: ${applicant.formData.get('ward')}, Status: ${applicant.status}`);
    });

  } catch (error) {
    console.error('❌ Error verifying applicants:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  verifyApplicants();
}

module.exports = { verifyApplicants };
