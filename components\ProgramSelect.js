import { useEffect, useState } from "react";
import axios from "axios";

export default function ProgramSelect({ onSelect }) {
  const [programs, setPrograms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchPrograms() {
      try {
        const response = await axios.get("/api/programs/fetch?formId=null");
        
        setPrograms(response.data.programs || []); // ✅ Extract programs array
      } catch (err) {
        console.error("Error fetching programs:", err);
        setError("Failed to load programs");
      } finally {
        setLoading(false);
      }
    }

    fetchPrograms();
  }, []);

  return (
    <div className="w-full">
      {loading && <p className="text-gray-500">Loading programs...</p>}
      {error && <p className="text-red-500">{error}</p>}
      {!loading && !error && (
      <select
      className="select bg-background text-accent select-bordered w-full"
      onChange={(e) => onSelect && onSelect(e.target.value)} // Ensure onSelect exists
      defaultValue="" // Use defaultValue instead of setting selected
    >
      <option value="" disabled>
        Select a program
      </option>
      {programs.map((program) => (
        <option className="overflow-x-auto" key={program._id} value={program._id}>
          {program.name}
        </option>
      ))}
    </select>
      )}
    </div>
  );
}
