import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import User from "@/models/User";
import bcrypt from "bcryptjs";
import { rateLimit } from "@/lib/ratelimit";
import { createLogger } from "@/lib/logger";

// Create logger for this route
const logger = createLogger({ service: "api/user/change-password" });

// Create rate limiter for password changes
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // Maximum password change attempts allowed
  keyPrefix: "password-change",
});

export async function PUT(request) {
  // Apply rate limiting
  const { success, response } = await limiter(request);
  if (!success) {
    logger.warn("Rate limit exceeded for password change", { 
      ip: request.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown"
    });
    return response;
  }

  try {
    // Get the authenticated user session
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      logger.warn("Unauthorized password change attempt", {
        ip: request.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown"
      });
      return NextResponse.json(
        { error: "Unauthorized access" },
        { status: 401 }
      );
    }
    
    logger.info("Password change initiated", {
      userId: session.user.id
    });
    
    // Connect to the database
    await dbConnect();
    
    // Parse the request body
    const { currentPassword, newPassword } = await request.json();
    
    // Validate input
    if (!currentPassword || !newPassword) {
      logger.warn("Missing password fields", {
        userId: session.user.id,
        hasCurrentPassword: !!currentPassword,
        hasNewPassword: !!newPassword
      });
      return NextResponse.json(
        { error: "Current password and new password are required" },
        { status: 400 }
      );
    }
    
    // Find the user by email from session
    const user = await User.findOne({ _id: session.user.id });
    
    if (!user) {
      logger.error("User not found during password change", {
        userId: session.user.id
      });
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    // Check if user email is empty
    if (!user.email) {
      logger.warn("Password change not allowed for account without email", {
        userId: session.user.id
      });
      return NextResponse.json(
        { error: "Password change not allowed for accounts without email" },
        { status: 403 }
      );
    }
    
    // Verify current password
    const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
    
    if (!isPasswordValid) {
      logger.warn("Incorrect current password provided", {
        userId: session.user.id
      });
      return NextResponse.json(
        { error: "Current password is incorrect" },
        { status: 400 }
      );
    }
    
    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    // Update user's password
    user.new = false;
    user.password = hashedPassword;
    await user.save();
    
    logger.info("Password changed successfully", {
      userId: session.user.id
    });
    
    return NextResponse.json({
      success: true,
      message: "Password changed successfully"
    });
  } catch (error) {
    logger.error("Password change error", {
      error: error.message,
      stack: error.stack,
      userId: error.userId || "unknown"
    });
    
    return NextResponse.json(
      { error: "Failed to change password" },
      { status: 500 }
    );
  }
}