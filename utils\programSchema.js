import { z } from "zod";

const programSchema = z.object({
  name: z.string()
    .min(1, "Program name is required")
    .max(40, "Program name cannot exceed 40 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().min(1, "End date is required"),
  venue: z.string().min(2, "Venue must be at least 2 characters long").max(100, "Venue cannot exceed 100 characters").optional(),
  maxBeneficiaries: z.number().positive("Must be a positive number").min(1, "At least one beneficiary is required"),
});

export default programSchema;