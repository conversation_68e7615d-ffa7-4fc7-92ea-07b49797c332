import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import Applicant from "@/models/Applicant";
import Program from "@/models/Program";
import { withAuth } from "@/middleware/authMiddleware";
import { rateLimit } from "@/lib/ratelimit";
import { createLogger } from "@/lib/logger";

const logger = createLogger({ service: "get-applicants" });

const limiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // max 10 requests per minute per IP
  keyPrefix: "getApplicants"
});

async function handler(req, { params }) {
  const rateCheck = await limiter(req);
  if (!rateCheck.success) return rateCheck.response;

  try {
    await dbConnect();

    const { programId } = params;

    if (!programId) {
      logger.warn("Missing programId in request");
      return NextResponse.json({ error: "Program ID is required" }, { status: 400 });
    }

    const applicants = await Applicant.find({
      programId,
      status: "selected"
    }).lean();

    const program = await Program.findById(programId).lean();

    if (!program) {
      logger.warn("Program not found", { programId });
      return NextResponse.json({ error: "Program not found" }, { status: 404 });
    }

    const filteredApplicants = applicants.map((applicant) => {
      const formData = applicant.formData || {};

      return {
        id: applicant._id.toString(),
        name: formData.name || "-",
        phone: formData.phone || formData.phonenumber || "-",
        state: formData.state || "-",
        ward: formData.ward || "-",
      };
    });

    logger.info("Applicants and program fetched successfully", {
      programId,
      applicantCount: filteredApplicants.length
    });

    return NextResponse.json(
      {
        program: {
          id: program._id.toString(),
          name: program.name || "-",
          description: program.description || "-",
          startDate: program.startDate || "-",
          endDate: program.endDate || "-",
          venue: program.venue || "-",
        },
        applicants: filteredApplicants,
      },
      { status: 200 }
    );
  } catch (error) {
    logger.error("Error fetching applicants", {
      error: error.message,
      stack: error.stack,
    });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export const GET = withAuth(handler);
