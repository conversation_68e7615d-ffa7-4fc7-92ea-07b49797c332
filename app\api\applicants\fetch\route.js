import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import Applicant from "@/models/Applicant";
import Program from "@/models/Program";

export async function GET(req) {
  try {
    await dbConnect();

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page")) || 1;
    const limit = parseInt(searchParams.get("limit")) || 10;
    const programId = searchParams.get("programId");
    const status = searchParams.get("status");
    const ward = searchParams.get("ward");
    const search = searchParams.get("search")?.trim();


    // Total applicants (no filter)
    const totalApplicants = await Applicant.countDocuments({});

    // Total beneficiaries (status = "selected")
    const totalBeneficiaries = await Applicant.countDocuments({ status: "selected" });

    // Total for program only (if programId passed)
    let programTotal = null;
    if (programId) {
      programTotal = await Applicant.countDocuments({ programId });
    }

    // Filtered query (search + program + status + ward)
    const query = {};
    if (programId) query.programId = programId;
    if (status) query.status = status;
    
    // Add ward filter (check ward in formData)
    if (ward) {
      query["formData.ward"] = ward;
    }

    if (search) {
      query.$or = [
        { "formData.name": { $regex: search, $options: "i" } },
        { "formData.email": { $regex: search, $options: "i" } },
        { "formData.phonenumber": { $regex: search, $options: "i" } },
      ];
    }

    

    // Count filtered applicants based on query
    const filteredTotal = await Applicant.countDocuments(query);
    const totalPages = Math.ceil(filteredTotal / limit);

    const applicants = await Applicant.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    // Get unique programIds and fetch program names
    const programIds = [...new Set(applicants.map(a => a.programId))];
    const programs = await Program.find({ _id: { $in: programIds } }, { _id: 1, name: 1 }).lean();
    const programMap = Object.fromEntries(programs.map(p => [p._id.toString(), p.name]));

    // Format response with program names
    const applicantsWithProgramNames = applicants.map(app => ({
      _id: app._id,
      programId: app.programId,
      programName: programMap[app.programId] || "Unknown Program",
      formPhase: app.formPhase,
      status: app.status,
      createdAt: app.createdAt,
      formData: {
        name: app.formData?.name || "N/A",
        email: app.formData?.email || "N/A",
        ward: app.formData?.ward || "N/A",
        phone: app.formData?.phonenumber || "N/A",
      },
    }));

    return NextResponse.json({
      applicants: applicantsWithProgramNames,
      totalApplicants,      // Total applicants without filter
      totalBeneficiaries,   // Total applicants with status "selected"
      programTotal,         // Total applicants for the program (if programId is provided)
      filteredTotal,        // Total applicants based on the filters
      totalPages,           // Total pages based on filtered applicants
      currentPage: page,    // Current page of the result
    }, { status: 200 });

  } catch (error) {
    console.error("Error fetching applicants:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}