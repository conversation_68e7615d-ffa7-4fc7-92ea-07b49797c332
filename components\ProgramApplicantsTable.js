"use client";
import DrawerLayout from "@/components/DrawerLayout";
import { useState, useEffect, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import TableSkeleton from "@/components/TableSkeleton";
import { useRouter } from "next/navigation";
import { wards } from "@/lib/wards";
import CountUp from "react-countup";
import { Loader2 } from "lucide-react";
// Custom components for the program applicants table
import ProgramApplicantsFilters from "@/components/ProgramApplicantsFilters";
import ProgramApplicantsTableBody from "@/components/ProgramApplicantsTableBody";
import ProgramApplicantsPagination from "@/components/ProgramApplicantsPagination";

export default function ProgramApplicantsTable({ programId }) {
  const router = useRouter();
  const [search, setSearch] = useState("");
  const [ward, setWard] = useState("all");
  const [status, setStatus] = useState("all");
  const [page, setPage] = useState(1);
  const limit = 10;
  const searchInputRef = useRef(null);

  // Debounce search input
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [isDebouncing, setIsDebouncing] = useState(false);
  
  useEffect(() => {
    setIsDebouncing(true);
    const timeout = setTimeout(() => {
      setDebouncedSearch(search);
      setIsDebouncing(false);
    }, 500);
    return () => clearTimeout(timeout);
  }, [search]);

  // Fetch applicants for the specific program
  const { data, isLoading, isError, isFetching } = useQuery({
    queryKey: ["programApplicants", programId, page, debouncedSearch, ward, status],
    queryFn: async () => {
      // Create params object first
      const params = {
        page,
        limit,
        programId
      };
      
      // Only add these params if they have valid values
      if (debouncedSearch) params.search = debouncedSearch;
      if (ward !== "all") params.ward = ward; // This needs to match the field in formData.ward
      if (status !== "all") params.status = status;
      
      // Convert to URLSearchParams
      const searchParams = new URLSearchParams();
      for (const [key, value] of Object.entries(params)) {
        searchParams.append(key, value);
      }
      
      
      const response = await axios.get(`/api/applicants/fetch?${searchParams.toString()}`);
      
      return response.data;
    },
    keepPreviousData: true,
    enabled: !!programId,
  });

  // Reset to page 1 when filters change
  useEffect(() => {
    setPage(1);
  }, [ward, status, debouncedSearch]);

  const resetFilters = () => {
    setSearch("");
    setWard("all");
    setStatus("all");
    setPage(1);
  };

  const getBadgeVariant = (status) => {
    switch (status) {
      case "approved":
        return "border-lime-600 w-20 text-lime-600";
      case "pending":
        return "border-amber-500 w-20 text-amber-500";
      case "rejected":
        return "border-rose-600 w-20 text-rose-600";
      case "selected":
        return "border-blue-500 w-20 text-blue-500";
      default:
        return "border-slate-500 w-20 text-slate-500";
    }
  };

  const handleViewDetails = (applicantId) => {
    router.push(`/applicants/view/${applicantId}`);
  };

  if (!programId) return <div className="text-center">No program selected</div>;
  if (isError) return <div className="text-center text-red-500">Failed to fetch applicants.</div>;
  
  return (
    <div className="relative">
      {/* Loading overlay (only shows during fetching, not debouncing) */}
      {isFetching && !isDebouncing && (
        <div className="absolute inset-0 bg-black/20 z-10 flex items-center justify-center">
          <Loader2 className="animate-spin text-white" size={24} />
        </div>
      )}
      
      <div className={`transition-opacity duration-200 ${isDebouncing ? 'opacity-50' : 'opacity-100'}`}>
        
        
        {/* Stats Summary - Always visible */}
        <div className="mb-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-card border border-slate-700 p-4 rounded-lg text-white">
            <h3 className="text-sm font-medium">Applicants </h3>
            <p className="text-2xl font-bold">Summary</p>
          </div>
          
          <div className="bg-card border border-slate-700 p-4 rounded-lg text-white">
            <h3 className="text-sm font-medium ">Program Total Applicants </h3>
            <p className="text-2xl font-bold">{data?.programTotal || 0}</p>
          </div>
          
          <div className="bg-card border border-slate-700 p-4 rounded-lg text-white">
            <h3 className="text-sm font-medium ">Filtered Results</h3>
            <p className="text-2xl font-bold">
              {data ? <CountUp end={data.filteredTotal || 0} duration={2} separator="," /> : 0}
            </p>
          </div>
        </div>
        <ProgramApplicantsFilters
          search={search}
          setSearch={setSearch}
          ward={ward}
          setWard={setWard}
          status={status}
          setStatus={setStatus}
          resetFilters={resetFilters}
          wards={wards}
          searchInputRef={searchInputRef}
        />
        {/* Table - Always visible with skeleton for loading state */}
        <div className="border border-slate-700 bg-card p-4 text-white rounded-lg overflow-x-auto">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Applicants List</h2>
            <p className="text-sm font-mono">
              Showing {data?.applicants?.length || 0} of {data?.filteredTotal || 0} applicants
            </p>
          </div>
          
          <Table>
            <TableHeader className="text-white">
              <TableRow className="border-b-slate-600">
                <TableHead className="text-white">Name</TableHead>
                <TableHead className="text-white">Contact</TableHead>
                <TableHead className="text-white">Ward</TableHead>
                <TableHead className="text-white">Status</TableHead>
                <TableHead className="text-white">Actions</TableHead>
              </TableRow>
            </TableHeader>

            <TableBody>
              {isLoading ? (
                // Inline table skeleton instead of replacing the whole table
                <>
                  {[...Array(5)].map((_, i) => (
                    <TableRow key={i} className="border-b border-slate-700">
                      {[...Array(5)].map((_, j) => (
                        <TableCell key={j}>
                          <div className="h-6 bg-slate-700/50 rounded animate-pulse"></div>
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </>
              ) : (
                <ProgramApplicantsTableBody 
                  applicants={data?.applicants} 
                  getBadgeVariant={getBadgeVariant} 
                  onViewDetails={handleViewDetails}
                />
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination - conditionally rendered based on data availability */}
        {data?.totalPages > 1 && (
          <ProgramApplicantsPagination page={page} setPage={setPage} totalPages={data.totalPages} />
        )}
      </div>
    </div>
  );
}