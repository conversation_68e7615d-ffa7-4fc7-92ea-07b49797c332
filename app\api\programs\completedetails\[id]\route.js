import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import Program from "@/models/Program";
import ProgramForm from "@/models/ProgramForm";

export async function GET(req, { params }) {
  try {
    await dbConnect();
    const { id } = await params;

    // Fetch program details
    const program = await Program.findById(id).lean();
    if (!program) {
      return NextResponse.json({ error: "Program not found" }, { status: 404 });
    }

    // Fetch the associated program form details
    const programForm = await ProgramForm.findOne({ programid: id }).lean();

    // Get base URL from environment variable
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;

    // Add full formLink if programForm and linkName exist
    const formLink = programForm?.formLink
      ? `${baseUrl}/forms/${programForm.formLink}`
      : null;

    // Include full formLink in response
    return NextResponse.json({ program, programForm: { ...programForm, formLink } });
  } catch (error) {
    console.error("Error fetching program details:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
