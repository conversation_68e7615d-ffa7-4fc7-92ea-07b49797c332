// app/api/profile/update/route.js
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import User from "@/models/User";
import { z } from "zod";

// Define validation schema for profile update
const profileUpdateSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name must be less than 100 characters").optional(),
  email: z.string().email("Invalid email format").optional(),
  phone: z.string().regex(/^\+?[0-9]{8,15}$/, "Phone number must be between 8-15 digits").optional(),
  bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
}).refine(data => Object.keys(data).length > 0, {
  message: "At least one field must be provided for update"
});

export async function PUT(request) {
  try {
    // Get the authenticated user session
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized access" },
        { status: 401 }
      );
    }
    
    // Connect to the database
    await dbConnect();
    
    // Parse the request body
    const data = await request.json();
    
    // Validate the input data
    const validationResult = profileUpdateSchema.safeParse(data);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: "Invalid input data", 
          details: validationResult.error.errors.map(err => ({
            path: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      );
    }
    
    // Find the user by id from session
    const user = await User.findOne({ _id: session.user.id });
    
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    // Fields that are allowed to be updated
    const allowedUpdates = ['name', 'email', 'phone', 'bio'];
    
    // Apply updates to allowed fields only (using validated data)
    const validatedData = validationResult.data;
    allowedUpdates.forEach(field => {
      if (validatedData[field] !== undefined) {
        user[field] = validatedData[field];
      }
    });
    
    // Save the updated user
    await user.save();
    
    // Return updated user data (excluding password)
    return NextResponse.json({
      success: true,
      message: "Profile updated successfully",
      data: {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        bio: user.bio,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    console.error("Profile update error:", error);
    
    // Check for duplicate key errors (email or phone)
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return NextResponse.json(
        { error: `The ${field} is already in use` },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: "Failed to update profile data" },
      { status: 500 }
    );
  }
}