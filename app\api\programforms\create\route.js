import { NextResponse } from "next/server";
import ProgramForm from "@/models/ProgramForm";
import Program from "@/models/Program";
import { dbConnect } from "@/lib/db";
import { z } from "zod";

// Zod Schema for validation
const formSchema = z.object({
  title: z.string().min(1, "Form title is required"),
  description: z.string().optional(),
  programid: z.string().min(1, "Program ID is required"),
  status: z.enum(["opened", "closed"]).default("opened"),
  fields: z
    .array(
      z.object({
        label: z.string().min(1, "Field label is required"),
        inputType: z.enum([
          "text",
          "email",
          "tel",
          "number",
          "date",
          "select",
          "textarea",
          "file",
        ]),
        required: z.boolean().default(false),
        options: z.array(z.string()).optional(),
      })
    )
    .optional()
    // Add a refine check to validate that field labels are unique
    .refine(
      (fields) => {
        if (!fields) return true;
        
        // Get all field labels
        const labels = fields.map(field => field.label);
        
        // Check if there are any duplicates by comparing the length of labels
        // with the length of a Set containing the labels (which removes duplicates)
        return labels.length === new Set(labels).size;
      },
      {
        message: "Duplicate field labels are not allowed",
        path: ["fields"], // This specifies which part of the data caused the error
      }
    ),
});

export async function POST(req) {
  try {
    await dbConnect(); // Connect to MongoDB
    
    const body = await req.json();
    
    const parsedData = formSchema.parse(body); // Validate input with Zod
    
    // Fetch program details using programid
    const program = await Program.findById(parsedData.programid);
    if (!program) {
      return NextResponse.json({ error: "Program not found" }, { status: 404 });
    }
    
    // Use the stored `linkName` instead of generating it from `program.name`
    const formLink = program.linkName;
    
    // Create new program form with generated formLink
    const newForm = await ProgramForm.create({
      ...parsedData,
      formLink,
    });
    
    program.formId = newForm._id; // Link the form to the program
    
    await program.save();
        
    return NextResponse.json(
      { message: "Program form created", form: newForm },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating program form:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 });
    }
    
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}