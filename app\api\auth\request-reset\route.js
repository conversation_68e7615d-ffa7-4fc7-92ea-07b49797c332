import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import User from "@/models/User";
import crypto from "crypto";
import { sendResetEmail } from "@/lib/sendResetEmail";
import { createLogger } from "@/lib/logger";
import { rateLimit } from "@/lib/ratelimit";

// Create a logger instance for this route
const logger = createLogger({ service: "password-reset-api" });

// Token expiration time: 1 hour
const TOKEN_EXPIRATION = 60 * 60 * 1000;

// Apply strict rate limiting for password reset attempts to prevent abuse
const applyRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minute window
  max: 10, // 10 attempts per 15 minutes per IP
  keyPrefix: "password-reset-request" // Unique prefix for this endpoint
});

export async function POST(req) {
  try {
    // Apply rate limiting first
    const rateLimitResult = await applyRateLimit(req);
    if (!rateLimitResult.success) {
      const ip = req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown";
      logger.warn("Rate limit exceeded for password reset", {
        ip,
        path: req.nextUrl.pathname
      });
      return rateLimitResult.response;
    }

    const ip = req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown";
    logger.info("Password reset request initiated", {
      ip,
      path: req.nextUrl.pathname
    });

    await dbConnect();
    const { email } = await req.json();
    
    if (!email) {
      logger.warn("Invalid password reset request - missing email", { ip });
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    const user = await User.findOne({ email });
    if (!user) {
      // Still log the attempt but don't expose whether the email exists
      logger.info("Password reset requested for non-existent email", { 
        attemptedEmail: email,
        ip
      });
      
      // Return success anyway to prevent email enumeration
      return NextResponse.json({ message: "If the email exists, a reset link has been sent" });
    }

    // Generate secure random token
    const resetToken = crypto.randomBytes(32).toString("hex");
    const tokenExpiry = Date.now() + TOKEN_EXPIRATION;

    // Update user with new token
    user.resetToken = resetToken;
    user.resetTokenExpires = tokenExpiry;
    await user.save();

    // Send the email
    try {
      await sendResetEmail(user.email, resetToken);
      logger.info("Reset email sent successfully", { 
        email: user.email,
        tokenExpiry: new Date(tokenExpiry).toISOString()
      });
    } catch (emailError) {
      logger.error("Failed to send reset email", {
        error: emailError.message,
        email: user.email
      });
      return NextResponse.json({ error: "Failed to send reset email" }, { status: 500 });
    }

    return NextResponse.json({ message: "If the email exists, a reset link has been sent" });
  } catch (error) {
    logger.error("Password reset request error", {
      error: error.message,
      stack: error.stack,
      path: req.nextUrl?.pathname || "unknown"
    });
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}