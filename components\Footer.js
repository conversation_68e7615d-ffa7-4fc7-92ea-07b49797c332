import Link from "next/link";
import { Facebook, Twitter, Instagram, Mail, Phone, MapPin } from "lucide-react";

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-primary text-white py-8 mt-auto">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Organization Info */}
          <div>
            <h3 className="text-xl font-bold mb-4"><PERSON>runa <PERSON>da Community Foundation</h3>
            <p className="text-sm mb-4">Supporting community development and empowerment initiatives.</p>
            <div className="flex space-x-4">
              <Link href="https://facebook.com" className="hover:text-blue-400 transition-colors">
                <Facebook size={20} />
              </Link>
              <Link href="https://twitter.com" className="hover:text-blue-400 transition-colors">
                <Twitter size={20} />
              </Link>
              <Link href="https://instagram.com" className="hover:text-blue-400 transition-colors">
                <Instagram size={20} />
              </Link>
            </div>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-bold mb-4">Contact Us</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Mail size={16} />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Phone size={16} />
                <span>+234 XXX XXX XXXX</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <MapPin size={16} />
                <span>Nigeria</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-white/20 mt-8 pt-4 text-center text-sm">
          <p>© {currentYear} Haruna Maiwada Community Foundation. All rights reserved.</p>
          <p className="mt-2">
            Developed by{" "}
            <Link
              href="https://sydatech.com.ng"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-300 hover:text-blue-200 transition-colors underline"
            >
              Sydatech
            </Link>
          </p>
        </div>
      </div>
    </footer>
  );
}
