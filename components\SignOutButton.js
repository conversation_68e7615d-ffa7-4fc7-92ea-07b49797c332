"use client";

import { signOut, useSession } from "next-auth/react";
import { useState } from "react";

export default function SignOutButton() {
  const { data: session } = useSession();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  if (!session) return null; // Hide if not signed in

  const handleSignOut = async () => {
    setIsLoggingOut(true);
    try {
      await signOut({ callbackUrl: "/" }); // Redirect to home after sign-out
    } catch (error) {
      setIsLoggingOut(false); // Reset loading state on error
    }
  };

  return (
    <button
      onClick={handleSignOut}
      disabled={isLoggingOut}
      className="btn w-full bg-red-950 border rounded-md border-none text-white font-bold hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {isLoggingOut ? "Logging out..." : "Logout"}
    </button>
  );
}