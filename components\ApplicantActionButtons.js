'use client'

import { useState } from "react"
import { Check, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { toast } from "sonner"
import { useSession } from "next-auth/react"
import { useQueryClient } from "@tanstack/react-query"
import axios from "axios"

export default function ApplicantActionButtons({ applicantId, programid }) {
  const { data: session } = useSession()
  const queryClient = useQueryClient()
  const [isLoading, setIsLoading] = useState({
    approve: false,
    reject: false
  })

  const handleAction = async (action) => {
    if (!session?.user?.id) {
      toast.error("Authentication Error", {
        description: "You must be logged in to perform this action."
      })
      return
    }

    const reviewerId = session.user.id
    const status = action === "approve" ? "approved" : "rejected"
    
    setIsLoading(prev => ({ ...prev, [action]: true }))
    
    try {
      const endpoint = action === "approve" ? 
        "/api/applicants/approve" : 
        "/api/applicants/reject"
      
      const response = await axios.patch(endpoint, { applicantId, reviewerId })
      
      toast.success(`Application ${status}`, {
        description: `The application has been successfully ${status} ${action === "approve" ? "✅" : "❌"}.`
      })

      await queryClient.invalidateQueries({ queryKey: ['programApplicants', programid] })
      await queryClient.invalidateQueries({ queryKey: ['applicant', applicantId] })
      await queryClient.invalidateQueries({ queryKey: ['applicants'] })
      setTimeout(() => {
        window.history.back();  // This navigates to the previous page
      }, 2000);
    } catch (error) {
      
      toast.error("Action Failed", {
        description:
          error.response?.data?.error ||
          error.response?.data?.message ||
          error.message ||
          `Failed to ${action} application. Please try again.`
      })
    } finally {
      setIsLoading(prev => ({ ...prev, [action]: false }))
    }
  }

  return (
    <div className="flex justify-end gap-4">
      <Button 
        onClick={() => handleAction("reject")}
        disabled={isLoading.reject || isLoading.approve}
        className="bg-rose-900 hover:bg-rose-600 text-white"
      >
        <X className="mr-2 h-4 w-4" />
        {isLoading.reject ? "Processing..." : "Reject"}
      </Button>
      
      <Button 
        onClick={() => handleAction("approve")}
        disabled={isLoading.approve || isLoading.reject}
        className="bg-lime-900 hover:bg-lime-600 text-white"
      >
        <Check className="mr-2 h-4 w-4" />
        {isLoading.approve ? "Processing..." : "Approve"}
      </Button>
    </div>
  )
}
