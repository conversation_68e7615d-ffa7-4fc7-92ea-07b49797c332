"use client";
import { But<PERSON> } from "@/components/ui/button";

export default function ApplicantsPagination({ page, setPage, totalPages }) {
  return (
    <div className="flex flex-col items-center mt-4 gap-3">
      <span className="text-center font-medium">
        Page {page} of {totalPages}
      </span>
      
      <div className="flex justify-between w-full max-w-sm gap-4">
        <Button 
          className="bg-card text-white flex-1" 
          disabled={page === 1} 
          onClick={() => setPage((prev) => prev - 1)}
        >
          Previous
        </Button>
        
        <Button
          className="bg-card text-white flex-1"
          disabled={page === totalPages}
          onClick={() => setPage((prev) => prev + 1)}
        >
          Next
        </Button>
      </div>
    </div>
  );
}