"use client"
import DrawerLayout from "@/components/DrawerLayout"
import { useState, useEffect } from "react"
import { useQuery } from "@tanstack/react-query"
import axios from "axios"
import { useRouter } from "next/navigation"
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table"
import { Card, CardContent } from "@/components/ui/card"
import { Users, CheckCircle, ScanText, MoreHorizontal } from "lucide-react"
import CountUp from "react-countup"
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu"
import AuthOnly from "@/components/AuthOnly"
// Import the new BeneficiariesFilters component
import BeneficiariesFilters from "@/components/BeneficiariesFilters"
import ApplicantsPagination from "@/components/ApplicantsPagination"

// Beneficiaries Stats Cards Component
function BeneficiaryStatsCards({ totalBeneficiaries, filteredTotal, programTotal }) {
  return (
    
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
       <Card className="bg-card border  border-slate-700 text-white">
        <CardContent className="pt-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-white">Beneficiaries</p>
              <h3 className="text-2xl font-bold">
               Stats
              </h3>
            </div>
            <ScanText className="h-8 w-8 text-white" />
          </div>
        </CardContent>
      </Card>
      <Card className="bg-card border border-slate-700 text-white">
        <CardContent className="pt-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-white">Total Beneficiaries</p>
              <h3 className="text-2xl font-bold">{totalBeneficiaries?.toLocaleString() || 0}</h3>
            </div>
            <Users className="h-8 w-8 text-white" />
          </div>
        </CardContent>
      </Card>

      <Card className="bg-card border border-slate-700 text-white">
        <CardContent className="pt-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-white">Showing Results</p>
              <h3 className="text-2xl font-bold">
                <CountUp end={filteredTotal || 0} duration={1.5} separator="," />
              </h3>
            </div>
            <CheckCircle className="h-8 w-8 text-white" />
          </div>
        </CardContent>
      </Card>

     
    </div>
  )
}

// Skeleton row component for loading state
function SkeletonRow() {
  return (
    <TableRow className="border-b-slate-600">
      <TableCell>
        <div className="h-4 w-32 bg-slate-700 rounded animate-pulse"></div>
      </TableCell>
      <TableCell>
        <div className="h-4 w-36 bg-slate-700 rounded animate-pulse mb-2"></div>
        <div className="h-4 w-28 bg-slate-700 rounded animate-pulse"></div>
      </TableCell>
      <TableCell>
        <div className="h-4 w-24 bg-slate-700 rounded animate-pulse"></div>
      </TableCell>
      <TableCell>
        <div className="h-4 w-16 bg-slate-700 rounded animate-pulse"></div>
      </TableCell>
      <TableCell>
        <div className="h-8 w-8 rounded-full bg-slate-700 animate-pulse"></div>
      </TableCell>
    </TableRow>
  )
}

// Beneficiaries Table Body Component
function BeneficiariesTableBody({ beneficiaries, isLoading }) {
  const router = useRouter()

  if (isLoading) {
    // Render skeleton rows when loading
    return Array(5)
      .fill(0)
      .map((_, index) => <SkeletonRow key={`skeleton-${index}`} />)
  }

  if (!beneficiaries || beneficiaries.length === 0) {
    return (
      <TableRow>
        <TableCell colSpan={5} className="text-center py-4">
          No beneficiaries found.
        </TableCell>
      </TableRow>
    )
  }

  return beneficiaries.map((beneficiary) => (
    <TableRow key={beneficiary._id} className="border-b-slate-600">
      <TableCell>
        <div className="font-medium">{beneficiary.formData.name}</div>
      </TableCell>
      <TableCell>
        <div className="text-sm text-white">{beneficiary.formData.email}</div>
        <div className="text-sm text-white">{beneficiary.formData.phone}</div>
      </TableCell>
      <TableCell>{beneficiary.programName}</TableCell>
      <TableCell>
        {/* Display ward instead of status */}
        {beneficiary.formData.ward || "N/A"}
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="p-2 rounded-full bg-blue-900 hover:bg-blue-700">
              <MoreHorizontal size={20} />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="bg-card text-white border border-slate-700" align="end">
            <DropdownMenuItem
              onClick={() => router.push(`/beneficiaries/view/${beneficiary._id}`)}
              className="font-semibold"
            >
              View Details
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  ))
}

// Main Beneficiaries Table Component
export default function BeneficiariesTable() {
  const [search, setSearch] = useState("")
  const [programId, setProgramId] = useState("all")
  const [ward, setWard] = useState("all")
  const [page, setPage] = useState(1)
  const limit = 10

  // Add state for program pagination
  const [programPage, setProgramPage] = useState(1)
  const programLimit = 5

  // Debounce search input
  const [debouncedSearch, setDebouncedSearch] = useState("")
  useEffect(() => {
    const timeout = setTimeout(() => setDebouncedSearch(search), 500)
    return () => clearTimeout(timeout)
  }, [search])

  // Fetch beneficiaries (selected applicants)
  const { data, isLoading, isError } = useQuery({
    queryKey: ["beneficiaries", page, debouncedSearch, programId, ward],
    queryFn: async () => {
      const params = new URLSearchParams({
        page,
        limit,
        search: debouncedSearch,
        status: "selected", // Always filter by selected status
        ...(programId !== "all" && { programId }),
        ...(ward !== "all" && { ward }), // Use ward filter instead of status
      }).toString()

      const response = await axios.get(`/api/applicants/fetch?${params}`)
     
      return response.data
    },
    keepPreviousData: true,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Fetch programs for filter dropdown with pagination
  const { data: programsData } = useQuery({
    queryKey: ["programs", programPage, programLimit],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: programPage.toString(),
        limit: programLimit.toString(),
        fields: "name,_id",
      }).toString()

      const response = await axios.get(`/api/programs/fetch?${params}`)
      
      return response.data
    },
    keepPreviousData: true,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Function to handle program pagination
  const handleProgramPageChange = (newPage) => {
   
    setProgramPage(newPage)
  }

  // Debug logging for pagination
  useEffect(() => {
    console.log("Program pagination state:", {
      programPage,
      programTotalPages: programsData?.pagination?.pages || 1,
      programsCount: programsData?.programs?.length || 0
    })
  }, [programPage, programsData])

  const resetFilters = () => {
    setSearch("")
    setProgramId("all")
    setWard("all")
    setPage(1)
  }

  if (isError) return <div className="text-center text-red-500">Failed to fetch beneficiaries.</div>

  // Calculate program pagination values from the API response
  const programTotalPages = programsData?.pagination?.pages || 1

  return (
    <AuthOnly>
    <DrawerLayout>
      <div className="p-4">
        {/* Stats Cards */}
        <BeneficiaryStatsCards
          totalBeneficiaries={data?.totalBeneficiaries || 0}
          filteredTotal={data?.filteredTotal || 0}
          programTotal={data?.programTotal || 0}
        />

        {/* Use the extracted BeneficiariesFilters component with corrected props */}
        <BeneficiariesFilters
          search={search}
          setSearch={setSearch}
          programId={programId}
          setProgramId={setProgramId}
          ward={ward}
          setWard={setWard}
          resetFilters={resetFilters}
          programsData={programsData?.programs || []}
          programPage={programPage}
          programTotalPages={programTotalPages}
          onProgramPageChange={handleProgramPageChange}
        />

        <div className="border border-slate-700 bg-card p-4 text-white rounded-lg overflow-x-auto">
          <Table>
            <TableHeader className="text-white">
              <TableRow className="border-b-slate-600">
                <TableHead className="text-white">Name</TableHead>
                <TableHead className="text-white">Contact</TableHead>
                <TableHead className="text-white">Program Name</TableHead>
                <TableHead className="text-white">Ward</TableHead>
                <TableHead className="text-white">Actions</TableHead>
              </TableRow>
            </TableHeader>

            <TableBody>
              <BeneficiariesTableBody beneficiaries={data?.applicants} isLoading={isLoading} />
            </TableBody>
          </Table>
        </div>

        {data?.totalPages > 1 && <ApplicantsPagination page={page} setPage={setPage} totalPages={data.totalPages} />}
      </div>
    </DrawerLayout></AuthOnly>
  )
}