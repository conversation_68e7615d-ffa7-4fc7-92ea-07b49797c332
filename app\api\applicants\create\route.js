import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import ProgramForm from "@/models/ProgramForm";
import Program from "@/models/Program";
import Applicant from "@/models/Applicant";
import { v2 as cloudinary } from "cloudinary";
import { Readable } from "stream";
import slugify from "slugify";
import { generateDummyApplicants } from "@/lib/dummyapplicants";
import { sanitizeInput } from "@/lib/sanitize";
import { rateLimit } from "@/lib/ratelimit";
import { generateFormSchema } from "@/lib/generateFormSchema";
import { createLogger } from "@/lib/logger";
import mongoose from "mongoose";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

const logger = createLogger({ service: "api/applicant" });

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100,
  keyPrefix: "applicant-create",
});

// Helper function to upload buffer to Cloudinary
async function uploadToCloudinary(buffer, folderName, fileName) {
  return new Promise((resolve, reject) => {
    const uploadOptions = {
      folder: folderName,
      public_id: fileName.split('.')[0], // Use filename without extension as public_id
      resource_type: "auto",
    };

    // Create a readable stream from the buffer
    const stream = Readable.from(buffer);
    
    const uploadStream = cloudinary.uploader.upload_stream(
      uploadOptions,
      (error, result) => {
        if (error) return reject(error);
        resolve(result);
      }
    );

    stream.pipe(uploadStream);
  });
}

export async function POST(req) {
  const ip = req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown";
  logger.info(`Received application submission request`, { ip });

  const { success, response } = await limiter(req);
  if (!success) {
    logger.warn(`Rate limit exceeded`, { ip });
    return response;
  }

  try {
    await dbConnect();
   
    let formData;
    let formDataObject = {};
    const contentType = req.headers.get("content-type") || "";

    if (contentType.includes("application/json")) {
      formDataObject = await req.json();
      logger.info(`Received JSON data`);
    } else {
      formData = await req.formData();
      for (const [key, value] of formData.entries()) {
        if (!(value instanceof File)) {
          formDataObject[key] = value;
        }
      }
    }

    // Sanitize input
    formDataObject = sanitizeInput(formDataObject);

    const programId = formDataObject.programid;
    const phone = formDataObject.phonenumber;
    const deviceFingerprint = formDataObject.deviceFingerprint;
    const isDummy = formDataObject.dummy;

    if (!programId || !phone) {
      logger.warn(`Missing required fields`, { programId, phone: !!phone });
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    if (isDummy) {
      const numApplicants = parseInt(formDataObject.numApplicants) || 1;
      logger.info(`Creating ${numApplicants} dummy applicants`);
      const dummyApplicants = generateDummyApplicants(numApplicants);

      try {
        // Use insertMany with ordered: false to continue inserting even if some fail due to duplicates
        const result = await Applicant.insertMany(dummyApplicants, { ordered: false });
        logger.info(`Successfully created ${result.length} dummy applicants`);
        return NextResponse.json({
          message: `Successfully created ${result.length} dummy applicants`,
          applicants: result
        }, { status: 201 });
      } catch (insertError) {
        // Handle bulk insert errors (some may succeed, some may fail due to duplicates)
        if (insertError.writeErrors) {
          const successCount = numApplicants - insertError.writeErrors.length;
          const duplicateCount = insertError.writeErrors.filter(err => err.code === 11000).length;

          logger.warn(`Dummy applicant creation completed with some duplicates`, {
            requested: numApplicants,
            successful: successCount,
            duplicates: duplicateCount
          });

          return NextResponse.json({
            message: `Created ${successCount} dummy applicants (${duplicateCount} duplicates skipped)`,
            successful: successCount,
            duplicates: duplicateCount
          }, { status: 201 });
        }
        throw insertError; // Re-throw if it's not a bulk write error
      }
    }

    // Check for existing applicant with better error handling
    const existingApplicant = await Applicant.findOne({
      programId,
      $or: [
        { "formData.phonenumber": phone },
        ...(deviceFingerprint ? [{ deviceFingerprint }] : [])
      ],
    });

    if (existingApplicant) {
      // Determine the reason for duplicate
      const isDuplicatePhone = existingApplicant.formData.get('phonenumber') === phone;
      const isDuplicateDevice = existingApplicant.deviceFingerprint === deviceFingerprint;

      let errorMessage = "An application already exists";
      if (isDuplicatePhone && isDuplicateDevice) {
        errorMessage = "An application with this phone number and device already exists";
      } else if (isDuplicatePhone) {
        errorMessage = "An application with this phone number already exists";
      } else if (isDuplicateDevice) {
        errorMessage = "An application has already been submitted from this device";
      }

      logger.warn(`Duplicate application attempt`, {
        programId,
        phone,
        deviceFingerprint,
        isDuplicatePhone,
        isDuplicateDevice
      });
      return NextResponse.json({ error: errorMessage }, { status: 409 });
    }

    const form = await ProgramForm.findOne({ programid: programId });
    if (!form) {
      logger.error(`Program form not found`, { programId });
      return NextResponse.json({ error: "Program form not found" }, { status: 404 });
    }

    if (form.status !== "opened") {
      logger.warn(`Attempt to submit to closed form`, { programId, formStatus: form.status });
      return NextResponse.json({ error: "Form is currently closed" }, { status: 400 });
    }

    // Check if the associated program is completed
    const program = await Program.findOne({
      $or: [
        { _id: programId },
        { linkName: programId }
      ]
    });
    if (!program) {
      logger.error(`Program not found`, { programId });
      return NextResponse.json({ error: "Program not found" }, { status: 404 });
    }

    if (program.status === "completed") {
      logger.warn(`Attempt to submit application to completed program`, { programId, programStatus: program.status });
      return NextResponse.json({ error: "Cannot submit applications to a completed program" }, { status: 400 });
    }

    // Process file uploads to Cloudinary
    const uploadedFiles = {};
    
    if (formData) {
      logger.info(`Processing form data with potential file uploads`);
      
      for (const field of form.fields) {
        if (field.inputType === "file") {
          const slugifiedLabel = slugify(field.label, { lower: true });
          const file = formData.get(slugifiedLabel);
          
          if (file instanceof File) {
            const fileNameBase = file.name.replace(/\s/g, "_");
            const fileName = `${Date.now()}_${fileNameBase}`;
            const folderName = `applicants/${programId}`; // Organize by program
            
            try {
              const arrayBuffer = await file.arrayBuffer();
              const buffer = Buffer.from(arrayBuffer);
              
              // Upload to Cloudinary
              const uploadResult = await uploadToCloudinary(buffer, folderName, fileName);
              
              // Store secure URL with slugified field name
              uploadedFiles[slugifiedLabel] = uploadResult.secure_url;
              
              // Also add to formDataObject for validation
              formDataObject[slugifiedLabel] = uploadResult.secure_url;
              
              logger.info(`File uploaded to Cloudinary successfully`, { 
                fieldLabel: slugifiedLabel, 
                fileName, 
                fileUrl: uploadResult.secure_url,
                resourceType: uploadResult.resource_type
              });
            } catch (fileError) {
              logger.error(`Cloudinary upload failed`, { 
                fieldLabel: slugifiedLabel, 
                fileName: file.name, 
                error: fileError.message 
              });
              return NextResponse.json({ 
                error: "File upload failed", 
                details: fileError.message 
              }, { status: 500 });
            }
          }
        }
      }
    }

    // Modify field objects for validation to use slugified labels
    const slugifiedFields = form.fields.map(field => {
      return {
        ...field,
        label: slugify(field.label, { lower: true })
      };
    });

    // Generate validation schema using slugified fields
    const validationSchema = generateFormSchema(slugifiedFields);

    // Transform number fields for validation
    for (const field of slugifiedFields) {
      if (field.inputType === "number" && formDataObject[field.label]) {
        formDataObject[field.label] = Number(formDataObject[field.label]);
      }
    }

    // Validate form data
    try {
      validationSchema.parse(formDataObject);
      logger.info(`Form data validation passed`);
    } catch (validationError) {
      const formattedErrors = validationError.errors.map(err => ({
        path: err.path.join('.'),
        message: err.message
      }));
      
      logger.warn(`Validation errors`, { errors: formattedErrors });
      return NextResponse.json({ 
        error: "Validation error", 
        details: formattedErrors 
      }, { status: 400 });
    }

    // Create the applicant with the form data (which already has slugified keys)
    const newApplicant = new Applicant({
      programId,
      formData: formDataObject, // Already contains uploaded Cloudinary file URLs
      formPhase: form.formPhase,
      deviceFingerprint: deviceFingerprint || null, // Store device fingerprint
    });

    try {
      await newApplicant.save();
      logger.info(`Application submitted successfully`, { applicantId: newApplicant._id, programId });
      return NextResponse.json({ message: "Application submitted successfully", applicant: newApplicant }, { status: 201 });
    } catch (saveError) {
      logger.error(`Error saving applicant`, { error: saveError.message, programId, phone });

      // Check if it's a duplicate key error (E11000) from the unique index
      if (saveError.code === 11000) {
        if (saveError.message.includes('unique_phone_per_program')) {
          return NextResponse.json({ error: "An application with this phone number already exists" }, { status: 409 });
        } else if (saveError.message.includes('unique_device_per_program')) {
          return NextResponse.json({ error: "An application has already been submitted from this device" }, { status: 409 });
        }
        return NextResponse.json({ error: "Duplicate application detected" }, { status: 409 });
      }

      throw saveError; // Re-throw other errors
    }

  } catch (error) {
    logger.error(`Error submitting application`, { error: error.message, stack: error.stack });
    return NextResponse.json({ error: "Internal server error", details: error.message }, { status: 500 });
  }
}