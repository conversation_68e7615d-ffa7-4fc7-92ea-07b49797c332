import { dbConnect } from "@/lib/db";
import User from "@/models/User";
import bcrypt from "bcryptjs";
import { NextResponse } from "next/server";
import { z } from "zod";
import { createLogger } from "@/lib/logger";
import { rateLimit } from "@/lib/ratelimit";

// Create a logger instance for this route
const logger = createLogger({ service: "password-reset-complete-api" });

// Apply rate limiting for password reset completion
const applyRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minute window
  max: 5, // 5 attempts per 5 minutes per IP
  keyPrefix: "password-reset-complete" // Unique prefix for this endpoint
});

// Define Zod validation schema
const resetPasswordSchema = z.object({
  token: z.string().min(1, "Token is required"),
  newPassword: z.string()
    .min(6, "Password must be at least 6 characters")
    .max(100, "Password is too long")
});

export async function POST(req) {
  try {
    // Apply rate limiting first
    const rateLimitResult = await applyRateLimit(req);
    if (!rateLimitResult.success) {
      const ip = req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown";
      logger.warn("Rate limit exceeded for password reset completion", {
        ip,
        path: req.nextUrl.pathname
      });
      return rateLimitResult.response;
    }

    const ip = req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown";
    logger.info("Password reset completion initiated", {
      ip,
      path: req.nextUrl.pathname
    });

    await dbConnect();
    
    const body = await req.json();
    
    // Validate request body
    const result = resetPasswordSchema.safeParse(body);
    
    if (!result.success) {
      const errorMessage = result.error.errors[0]?.message || "Invalid input";
      logger.warn("Invalid password reset data", {
        errors: result.error.errors.map(e => e.message),
        ip
      });
      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }
    
    const { token, newPassword } = result.data;

    // For security, log only a portion of the token to avoid exposing full token in logs
    const tokenPreview = token.substring(0, 6) + '...';
    
    logger.debug("Looking up reset token", { tokenPreview });

    const user = await User.findOne({
      resetToken: token,
      resetTokenExpires: { $gt: Date.now() },
    });

    if (!user) {
      logger.warn("Invalid or expired reset token used", { 
        tokenPreview,
        ip,
        tokenFound: !!await User.findOne({ resetToken: token }),
        tokenExpired: !!await User.findOne({ 
          resetToken: token, 
          resetTokenExpires: { $lte: Date.now() } 
        })
      });
      return NextResponse.json({ error: "Invalid or expired token" }, { status: 400 });
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    // Update user record
    user.password = hashedPassword;
    user.resetToken = undefined;
    user.resetTokenExpires = undefined;
    await user.save();

    logger.info("Password reset successful", { 
      user: user.email,
      ip
    });

    return NextResponse.json({ message: "Password reset successful" });
    
  } catch (error) {
    logger.error("Password reset error", {
      error: error.message,
      stack: error.stack,
      path: req.nextUrl?.pathname || "unknown"
    });
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}