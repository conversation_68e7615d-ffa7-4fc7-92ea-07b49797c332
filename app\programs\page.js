"use client"
import React from 'react'
import ProgramsCard from '@/components/ProgramsCard'
import DrawerLayout from '@/components/DrawerLayout'
import { useRouter } from 'next/navigation'
import AuthOnly from '@/components/AuthOnly'
import AdminOnly from '@/components/AdminOnly'
function page() {
    const router = useRouter();  
  return (
    <AuthOnly>
       <DrawerLayout>
        <div className='min-h-screen  bg-background text-accent'>
       
           <AdminOnly>
            <div className=' mx-4 grid grid-cols-1 gap-5 md:grid-cols-3'>
            <button
            onClick={() => router.push("/programs/programcreation")}
            className="btn  p-4  w-full mt-5  rounded-lg bg-lime-900 border border-lime-700  hover:bg-lime-700  text-white"
          >
            + Create New Program
          </button>
          
          
  <style jsx>{`
    .scroll-hidden {
      scrollbar-width: none; 
      -ms-overflow-style: none; 
    }
    .scroll-hidden::-webkit-scrollbar {
      display: none;
    }
  `}</style>
          </div></AdminOnly>
          <div className="flex flex-col  items-center justify-center ">
            <ProgramsCard />
        </div> </div>
        </DrawerLayout></AuthOnly>
  )
}

export default page