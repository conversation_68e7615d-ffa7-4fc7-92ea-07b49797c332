"use client"

import { useRef, useEffect } from "react"
import { wards } from "@/lib/wards"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function BeneficiariesFilters({
  search,
  setSearch,
  programId,
  setProgramId,
  ward,
  setWard,
  resetFilters,
  programsData,
  programPage = 1,
  programTotalPages = 1,
  onProgramPageChange,
}) {
  const searchInputRef = useRef(null)

  // Extract the programs array from programsData object
  const programList = Array.isArray(programsData?.programs)
    ? programsData.programs
    : Array.isArray(programsData)
      ? programsData
      : []

  

  return (
    <div className="bg-card border border-slate-700 rounded-xl p-6 mb-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Search Input */}
        <div>
          <p className="mb-2 text-white text-sm">Search</p>
          <Input
            ref={searchInputRef}
            placeholder="Search by name, email or phone"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full bg-white text-white border border-slate-700 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-500 focus-visible:border-slate-500"
          />
        </div>

        {/* Program Filter */}
        <div>
          <p className="mb-2 text-white text-sm">Filter by Program</p>
          <div className="space-y-2">
            <Select value={programId} onValueChange={setProgramId}>
              <SelectTrigger className="w-full bg-background text-accent border border-slate-700 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-500 focus-visible:border-slate-500">
                <SelectValue placeholder="Filter by Program" />
              </SelectTrigger>
              <SelectContent className="bg-card text-white border border-slate-700">
                <SelectItem value="all">All Programs</SelectItem>
                {programList.length > 0 ? (
                  programList.map((program) => (
                    <SelectItem className="truncate" key={program._id} value={program._id}>
                      {program.name}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem disabled>No Programs Found</SelectItem>
                )}

                {/* Pagination Controls inside dropdown - always show for debugging */}
                <div className="flex items-center justify-between px-2 py-2 border-t border-slate-700 mt-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      if (onProgramPageChange && programPage > 1) {
                        onProgramPageChange(programPage - 1)
                      }
                    }}
                    disabled={programPage <= 1}
                    className="h-8 w-8 p-0 text-white hover:bg-slate-700"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm text-white">
                    Page {programPage} of {programTotalPages}
                  </span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      if (onProgramPageChange && programPage < programTotalPages) {
                        onProgramPageChange(programPage + 1)
                      }
                    }}
                    disabled={programPage >= programTotalPages}
                    className="h-8 w-8 p-0 text-white hover:bg-slate-700"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </SelectContent>
            </Select>

            {/* External Pagination Controls - Always show for debugging */}
            
          </div>
        </div>

        {/* Ward Filter */}
        <div>
          <p className="mb-2 text-white text-sm">Filter by Ward</p>
          <Select value={ward} onValueChange={setWard}>
            <SelectTrigger className="w-full bg-background text-accent border border-slate-700 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-500 focus-visible:border-slate-500">
              <SelectValue placeholder="Filter by Ward" />
            </SelectTrigger>
            <SelectContent className="bg-card text-white border border-slate-700">
              <SelectItem value="all">All Wards</SelectItem>
              {wards.map((wardOption) => (
                <SelectItem key={wardOption} value={wardOption}>
                  {wardOption}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Reset Filters Button */}
      <div className="flex justify-end mt-4">
        <Button onClick={resetFilters} className="bg-blue-900 text-white dark:text-white font-bold hover:bg-blue-700">
          Reset Filters
        </Button>
      </div>
    </div>
  )
}
