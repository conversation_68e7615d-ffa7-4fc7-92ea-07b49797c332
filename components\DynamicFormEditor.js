"use client";
import { useState, useRef, useEffect } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import axios from "axios";
import { toast } from "sonner";
import { wards } from "@/lib/wards";
import FieldComponent from "@/components/FieldComponent";
import { dynamicFormSchema } from "@/utils/dynamicFormSchema";
import { defaultFields, inputTypes } from "@/lib/default";
import { z } from "zod";
import { useQueryClient } from "@tanstack/react-query";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

export default function DynamicFormEditor({ initialProgramData, programid }) {
  const dialogRef = useRef(null);
  const formikRef = useRef(null);

  // Merge default fields with incoming fields
  const mergeWithDefaultFields = (incomingFields = []) => {
    const defaultLabels = defaultFields.map((f) => f.label);
    const nonDefaultFields = incomingFields.filter((field) => !defaultLabels.includes(field.label));
    return [...defaultFields, ...nonDefaultFields];
  };

  // Initialize fields only once when component mounts or initialProgramData changes significantly
  const [fields, setFields] = useState(() => mergeWithDefaultFields(initialProgramData?.fields));

  // Only update fields when initialProgramData changes from external sources, not from internal field operations
  useEffect(() => {
    if (initialProgramData && dialogRef.current?.open !== true) {
      setFields(mergeWithDefaultFields(initialProgramData?.fields));
    }
  }, [initialProgramData]);

  const handleOpenModal = () => {
    dialogRef.current.showModal();
  };

  const handleCloseModal = () => {
    if (formikRef.current) {
      formikRef.current.resetForm();
      // Reset fields to initial state
      setFields(mergeWithDefaultFields(initialProgramData?.fields));
    }
    dialogRef.current.close();
  };
  const queryClient = useQueryClient();
  // Validate the form for duplicate field labels
  const validateForm = (values) => {
    const errors = {};
    
    // Check for duplicate labels
    const labelMap = new Map();
    const duplicates = [];
    
    values.fields.forEach((field, index) => {
      const label = field.label.trim();
      if (label) {
        if (labelMap.has(label)) {
          duplicates.push(label);
        } else {
          labelMap.set(label, index);
        }
      }
    });
    
    if (duplicates.length > 0) {
      errors.fields = `Duplicate field labels found: ${duplicates.join(', ')}. Each label must be unique.`;
    }
    
    return errors;
  };

  // Generate unique field label
  const generateUniqueFieldLabel = (fields) => {
    let newLabel = "Field";
    let counter = 1;
    let labelExists = true;
    
    while (labelExists) {
      const candidateLabel = `${newLabel} ${counter}`;
      if (!fields.some(field => field.label === candidateLabel)) {
        newLabel = candidateLabel;
        labelExists = false;
      } else {
        counter++;
      }
    }
    
    return newLabel;
  };

  const addField = (values, setFieldValue) => {
    const uniqueLabel = generateUniqueFieldLabel(values.fields);
    const newFields = [
      ...values.fields,
      { label: uniqueLabel, inputType: "text", required: false, isDefault: false, options: [] },
    ];
    setFieldValue("fields", newFields);
  };

  const removeField = (index, values, setFieldValue) => {
    if (values.fields[index].isDefault) return;
    const newFields = values.fields.filter((_, i) => i !== index);
    setFieldValue("fields", newFields);
  };

  // Check if any field has validation errors
  const hasFieldErrors = (fields) => {
    return fields.some(field => field.error);
  };

  // Enhanced dynamicFormSchema to include error field and check for unique labels
  const enhancedDynamicFormSchema = z.object({
    title: z.string().min(1, "Title is required"),
    description: z.string().optional(),
    programid: z.string().min(1, "Program ID is required"),
    fields: z
      .array(
        z.object({
          label: z.string().min(1, "Field label is required"),
          inputType: z.enum(inputTypes),
          required: z.boolean(),
          isDefault: z.boolean().optional(),
          options: z.array(z.string()).optional(),
          error: z.string().optional(), // Add error field to schema
        })
      )
      .min(1, "At least one field is required")
  });

  return (
    <div>
      <button
        className="btn bg-card mr-4 w-60 border hover:bg-blue-900 border-slate-700 rounded-lg"
        onClick={handleOpenModal}
      >
        Edit/Create Application Form
      </button>

      <dialog id="my_modal_5" className="modal  backdrop-blur-sm bg-opacity-0 modal-middle" ref={dialogRef}>
        <div className="modal-box bg-card max-h-9/12 border border-slate-700 text-white">
          <h3 className="font-bold text-lg text-center">Create/Edit Program Application Form</h3>

          <Formik
            innerRef={formikRef}
            initialValues={{
              title: initialProgramData?.title || "",
              description: initialProgramData?.description || "",
              programid: programid,
              fields: fields,
            }}
            enableReinitialize={false} // Changed to false to prevent reinitialization
            validationSchema={toFormikValidationSchema(enhancedDynamicFormSchema)}
            validate={validateForm}
            onSubmit={async (values, { setSubmitting, resetForm }) => {
              try {
                // Check for duplicate labels
                const labelSet = new Set();
                const duplicates = [];
                
                for (const field of values.fields) {
                  if (labelSet.has(field.label)) {
                    duplicates.push(field.label);
                  } else {
                    labelSet.add(field.label);
                  }
                }
                
                if (duplicates.length > 0) {
                  toast.error(`Duplicate field labels found: ${duplicates.join(', ')}`);
                  setSubmitting(false);
                  return;
                }

                // Check if any field has errors
                if (hasFieldErrors(values.fields)) {
                  toast.error("Please fix all field errors before submitting");
                  setSubmitting(false);
                  return;
                }

                // Remove any error properties before submitting
                const cleanedFields = values.fields.map(field => {
                  const { error, ...cleanField } = field;
                  return cleanField;
                });
                
                const dataToSubmit = {
                  ...values,
                  fields: cleanedFields
                };

                const response = await axios.put(`/api/programforms/update/${programid}`, dataToSubmit);
                
                if (response.status === 200 || response.status === 201) {
                  toast.success("Program updated successfully! 🎉");
                  // Update the local fields state with the submitted values
                  setFields(cleanedFields);
                  resetForm({ values: dataToSubmit }); // Reset with cleaned values
                  handleCloseModal();
                   await queryClient.invalidateQueries(["programsSummary"]);
                  
                } else {
                  toast.error("Failed to update program. Please try again.");
                }
              } catch (error) {
                console.error("Error updating program:", error);
                toast.error("Something went wrong. Please try again.");
              }
              setSubmitting(false);
            }}
          >
            {({ values, setFieldValue, isSubmitting, errors, touched }) => (
              <Form className="space-y-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">Form Title</span>
                  </label>
                  <Field name="title">
                    {({ field }) => (
                      <Input 
                        {...field} 
                        type="text" 
                        className="w-full"
                      />
                    )}
                  </Field>
                  <ErrorMessage name="title" component="p" className="text-red-500 text-sm" />
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">Description</span>
                  </label>
                  <Field name="description">
                    {({ field }) => (
                      <Textarea 
                        {...field} 
                        className="w-full"
                      />
                    )}
                  </Field>
                </div>

                <h2 className="text-lg font-semibold">Fields</h2>
                
                {/* Form-level error for duplicate fields */}
                {errors.fields && typeof errors.fields === 'string' && (
                  <div className="alert alert-error text-sm p-2">
                    {errors.fields}
                  </div>
                )}
                
                <div className="grid md:grid-cols-2 grid-cols-1 gap-4 mb-4">
                  {values.fields?.map((field, index) => (
                    <FieldComponent
                      inputTypes={inputTypes}
                      key={`field-${index}`}
                      field={field}
                      index={index}
                      fields={values.fields}
                      setFields={(updatedFields) => {
                        // Check for duplicate labels before updating
                        const updatedField = updatedFields[index];
                        const isDuplicate = updatedFields.some(
                          (f, i) => i !== index && f.label.trim() === updatedField.label.trim()
                        );
                        
                        // Add or remove error based on duplicate check
                        if (isDuplicate) {
                          updatedFields[index] = {
                            ...updatedField,
                            error: "This label is already in use"
                          };
                        } else if (updatedField.error) {
                          // Remove error if no longer duplicate
                          const { error, ...rest } = updatedField;
                          updatedFields[index] = rest;
                        }
                        
                        setFieldValue("fields", updatedFields);
                      }}
                      removeField={() => removeField(index, values, setFieldValue)}
                    />
                  ))}
                </div>

                <button
                  type="button"
                  onClick={() => addField(values, setFieldValue)}
                  className="btn btn-outline flex items-center gap-2 w-full"
                >
                  Add Field
                </button>

                <div className="flex gap-2">
                  <button 
                    type="submit" 
                    className="btn btn-primary flex-1" 
                    disabled={isSubmitting || hasFieldErrors(values.fields)}
                  >
                    {isSubmitting ? "Updating..." : "Update Form"}
                  </button>
                  <button
                    type="button"
                    className="btn btn-outline flex-1"
                    onClick={handleCloseModal}
                  >
                    Cancel
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
        {/* Modal backdrop */}
        <form method="dialog" className="modal-backdrop">
          <button onClick={handleCloseModal}>close</button>
        </form>
      </dialog>
    </div>
  );
}