import Link from 'next/link';
import { AlertCircle } from 'lucide-react';

export const metadata = {
  title: 'Page Not Found | Maiwada Global',
  robots: {
    index: false,
    follow: true,
  },
}

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-background/80">
      <div className="p-8 rounded-xl bg-card border border-red-500/20 shadow-lg animate-fade-in max-w-md w-full text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h1 className="text-2xl font-bold text-red-500">Page Not Found</h1>
        <p className="text-muted-foreground mt-2 mb-6">The page you're looking for doesn't exist or has been moved.</p>
        <Link 
          href="/" 
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
        >
          Return Home
        </Link>
      </div>
    </div>
  );
}