import { dbConnect } from "@/lib/db";
import User from "@/models/User";
import bcrypt from "bcryptjs";
import { z } from "zod";
import { sanitizeInput } from "@/lib/sanitize";
import { rateLimit } from "@/lib/ratelimit";
import { withAdminAuth } from "@/middleware/authMiddleware";
import { createLogger } from "@/lib/logger";

// Create a logger for this route
const logger = createLogger({ service: "api/users" });

// Create a rate limiter for user creation
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limited number of user creations allowed
  keyPrefix: "user-create",
});

// Updated Zod schema with improved username validation
const userSchema = z.object({
  name: z.string().min(2, "Name is required"),
  username: z.string()
    .min(3, "Username is required")
    .max(25, "Username is too long")
    // Regex pattern that allows email format while preventing NoSQL injection
    .regex(/^[a-zA-Z0-9_.+-]+@?[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/, "Username contains invalid characters"),
  phone: z
    .string()
    .min(7, "Phone number too short")
    .max(19, "Phone number too long"),
});

async function handler(req) {
  // Apply rate limiting
  const { success, response } = await limiter(req);
  if (!success) {
    logger.warn("Rate limit exceeded for user creation", { 
      admin: req.user?.email,
      ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown"
    });
    return response;
  }

  logger.info("User creation attempt initiated", { 
    admin: req.user.email
  });

  await dbConnect();

  try {
    const rawBody = await req.json();
    
    // Sanitize input to prevent XSS attacks
    const body = sanitizeInput(rawBody);
    
    logger.info("Processing user creation data", { 
      admin: req.user.email
    });

    // Validate request body
    const parsed = userSchema.safeParse(body);

    if (!parsed.success) {
      const errorMessage = parsed.error.errors[0]?.message || "Invalid input";
      logger.warn("Validation failed for user creation", {
        admin: req.user.email,
        error: errorMessage,
        validationErrors: parsed.error.errors
      });
      return Response.json({ error: errorMessage }, { status: 400 });
    }

    const { name, username, phone } = parsed.data;

    // Check if username already exists
    const existingUser = await User.findOne({ username });
    if (existingUser) {
      logger.warn("Username already exists", {
        admin: req.user.email,
        username: username
      });
      return Response.json({ error: "Username already exists" }, { status: 400 });
    }

    // Hardcoded password
    const hashedPassword = await bcrypt.hash("123456", 10);

    // Create new user
    const user = await User.create({
      name,
      username,
      phone,
      password: hashedPassword,
    });

    logger.info("User created successfully", {
      admin: req.user.email,
      userId: user._id,
      username: username
    });

    return Response.json(
      { message: "User created successfully", userId: user._id },
      { status: 201 }
    );
  } catch (error) {
    logger.error("User creation error", {
      admin: req.user?.email,
      error: error.message,
      stack: error.stack
    });
    return Response.json({ error: "Server error" }, { status: 500 });
  }
}

// Export the route handler with admin authentication middleware
export const POST = withAdminAuth(handler);