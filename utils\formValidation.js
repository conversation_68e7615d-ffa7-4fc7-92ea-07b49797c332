import { z } from "zod";

// Function to generate a Zod schema dynamically
export function generateFormSchema(fields) {
  const schemaShape = {};

  fields.forEach((field) => {
    let fieldSchema;
    switch (field.inputType) {
      case "text":
      case "tel":
        fieldSchema = field.required
          ? z.string().min(10, `invalid ${field.label} `)
          : z.string().optional();
        break;
      case "email":
        fieldSchema = field.required
          ? z.string().email("Invalid email address")
          : z.string().email("Invalid email address").optional();
        break;
      case "number":
        fieldSchema = field.required
          ? z.number({ invalid_type_error: `${field.label} must be a number` })
          : z.number({ invalid_type_error: `${field.label} must be a number` }).optional();
        break;
      case "date":
        fieldSchema = field.required
          ? z.string().refine((val) => !isNaN(Date.parse(val)), `${field.label} must be a valid date`)
          : z.string().refine((val) => val === "" || !isNaN(Date.parse(val)), `${field.label} must be a valid date`).optional();
        break;
      case "file":
        fieldSchema = field.required
          ? z.instanceof(File, { message: "A valid file is required" })
          : z.instanceof(File, { message: "A valid file is required" }).optional();
        break;
      case "select":
        fieldSchema = field.required
          ? z.string().min(1, `${field.label} is required`)
          : z.string().optional();
        break;
      case "textarea":
        fieldSchema = field.required
          ? z.string().min(1, `${field.label} is required`)
          : z.string().optional();
        break;
      default:
        fieldSchema = z.string().optional();
    }
    // Use field.label as the key
    schemaShape[field.label] = fieldSchema;
  });

  return z.object(schemaShape);
}
