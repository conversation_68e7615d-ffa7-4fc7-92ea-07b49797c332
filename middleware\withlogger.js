import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { createLogger } from "@/lib/logger"; // Assuming you renamed the file to logger.ts or logger.js

export function withLogger(handler, moduleName = "general") {
  return async (req) => {
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id || "anonymous";

    // Use createLogger to make a scoped child logger
    const logger = createLogger({
      module: moduleName,
      userId,
      path: req.nextUrl?.pathname,
      method: req.method,
    });

    // Pass logger in a custom object to the handler
    return handler(req, { logger });
  };
}
