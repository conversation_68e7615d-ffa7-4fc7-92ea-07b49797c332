import { useState, useEffect } from 'react';
import { generateDeviceFingerprint } from '@/lib/deviceFingerprint';

/**
 * Custom hook for device fingerprinting
 * @returns {Object} - Object containing fingerprint data and loading state
 */
export function useDeviceFingerprint() {
  const [fingerprint, setFingerprint] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let mounted = true;

    const generateFingerprint = async () => {
      try {
        setLoading(true);
        setError(null);

        // Small delay to ensure DOM is fully loaded
        await new Promise(resolve => setTimeout(resolve, 100));

        const result = generateDeviceFingerprint();
        
        if (mounted) {
          if (result && result.fingerprint) {
            setFingerprint(result);
          } else {
            setError('Failed to generate device fingerprint');
          }
        }
      } catch (err) {
        if (mounted) {
          setError(err.message || 'Error generating device fingerprint');
          console.error('Device fingerprint error:', err);
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    // Only run on client side
    if (typeof window !== 'undefined') {
      generateFingerprint();
    } else {
      setLoading(false);
    }

    return () => {
      mounted = false;
    };
  }, []);

  return {
    fingerprint: fingerprint?.fingerprint || null,
    fullFingerprint: fingerprint,
    loading,
    error,
    isReady: !loading && !error && fingerprint?.fingerprint
  };
}

/**
 * Simple hook that just returns the fingerprint string
 * @returns {string|null} - The device fingerprint string or null
 */
export function useSimpleDeviceFingerprint() {
  const { fingerprint } = useDeviceFingerprint();
  return fingerprint;
}
