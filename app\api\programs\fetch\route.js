import { NextResponse } from "next/server";
import Program from "@/models/Program";
import Applicant from "@/models/Applicant";
import { dbConnect } from "@/lib/db";

export async function GET(req) {
  try {
    await dbConnect();

    const { searchParams } = new URL(req.url);
    const fields = searchParams.get("fields");
    const formId = searchParams.get("formId");
    
    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "10", 10);
    const skip = (page - 1) * limit;

    // Allowed fields for selection
    const allowedFields = [
      "name", "description", "startDate", "endDate", "status", "linkName", 
      "maxBeneficiaries", "formId", "createdAt", "updatedAt"
    ];

    // Process fields selection
    let selectedFields = null;
    if (fields) {
      const requestedFields = fields.split(",").map(field => field.trim()).filter(field => allowedFields.includes(field));
      if (requestedFields.length > 0) {
        selectedFields = requestedFields.join(" ");
      }
    }

    // Build query filter
    let filter = {};
    if (formId === "null") {
      filter.formId = null;
    }

    // Get total count for pagination info
    const totalCount = await Program.countDocuments(filter);
    
    // Query programs with pagination and optional field selection
    const programs = await Program.find(filter)
      .select(selectedFields)
      .skip(skip)
      .limit(limit)
      .lean();

    // Get applicant counts for each program
    const programsWithCounts = await Promise.all(programs.map(async (program) => {
      const selectedApplicantsCount = await Applicant.countDocuments({
        programId: program._id || program.id,
        status: "selected"
      });
      
      return {
        ...program,
        selectedApplicantsCount
      };
    }));

    // Prepare pagination metadata
    const pagination = {
      total: totalCount,
      page,
      limit,
      pages: Math.ceil(totalCount / limit)
    };

    return NextResponse.json({ 
      programs: programsWithCounts,
      pagination
    }, { status: 200 });
  } catch (error) {
    console.error("Error fetching programs:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}