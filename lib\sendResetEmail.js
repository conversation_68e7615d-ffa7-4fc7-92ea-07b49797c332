export const sendResetEmail = async (toEmail, resetToken) => {
    const res = await fetch("https://api.brevo.com/v3/smtp/email", {
      method: "POST",
      headers: {
        "api-key": process.env.BREVO_API_KEY,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        sender: { name: "Haruna Maiwada Community Foundation", email: "<EMAIL>" },
        to: [{ email: toEmail }],
        subject: "Reset Your Password",
        htmlContent: `
          <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password - Haruna Maiwada Community Foundation</title>
    <style>
        /* Reset styles for email clients */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }

        /* Main styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #f4f7fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }

        .header-icon {
            width: 60px;
            height: 60px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 24px;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
            letter-spacing: -0.5px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 50px 30px;
        }

        .greeting {
            font-size: 18px;
            margin-bottom: 25px;
            color: #2c3e50;
        }

        .message {
            font-size: 16px;
            line-height: 1.7;
            margin-bottom: 35px;
            color: #555555;
        }

        .cta-container {
            text-align: center;
            margin: 40px 0;
        }

        .cta-button {
            display: inline-block;
            padding: 16px 32px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }

        .security-notice .icon {
            color: #f39c12;
            font-size: 20px;
            margin-right: 10px;
        }

        .security-notice p {
            margin: 0;
            color: #856404;
            font-size: 14px;
        }

        .alternative-link {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            font-size: 14px;
            color: #6c757d;
        }

        .alternative-link strong {
            color: #495057;
        }

        .footer {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 30px;
            text-align: center;
            font-size: 14px;
        }

        .footer-links {
            margin: 20px 0;
        }

        .footer-links a {
            color: #3498db;
            text-decoration: none;
            margin: 0 15px;
        }

        .footer-links a:hover {
            text-decoration: underline;
        }

        .divider {
            height: 1px;
            background: linear-gradient(to right, transparent, #e0e6ed, transparent);
            margin: 30px 0;
        }

        /* Responsive design */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                margin: 0 !important;
            }
            
            .header, .content, .footer {
                padding: 25px 20px !important;
            }
            
            .header h1 {
                font-size: 24px !important;
            }
            
            .cta-button {
                display: block !important;
                width: 100% !important;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="header-icon">🔐</div>
            <h1>Password Reset Request</h1>
            <p>Haruna Maiwada Community Foundation</p>
        </div>

        <!-- Main Content -->
        <div class="content">
            <div class="greeting">
                Hello there,
            </div>

            <div class="message">
                We received a request to reset the password for your account. If you made this request, please click the button below to create a new password.
            </div>

            <div class="cta-container">
                <a href="${process.env.NEXT_PUBLIC_BASE_URL}/reset-password?token=${resetToken}" class="cta-button">
                    Reset My Password
                </a>
            </div>

            <div class="security-notice">
                <p><span class="icon">⚠️</span> <strong>Important:</strong> This reset link will expire in 1 hour for your security. If you need a new link, please request another password reset.</p>
            </div>

            <div class="divider"></div>

            <div class="alternative-link">
                <strong>Having trouble with the button?</strong><br>
                Copy and paste this link into your browser:<br>
                <span style="word-break: break-all; color: #667eea;">${process.env.NEXT_PUBLIC_BASE_URL}/reset-password?token=${resetToken}</span>
            </div>

            <div class="message" style="margin-top: 30px; font-size: 14px; color: #7f8c8d;">
                If you didn't request this password reset, please ignore this email or contact our support team if you have concerns about your account security.
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Haruna Maiwada Community Foundation</strong></p>
            <div class="footer-links">
                <a href="#">Contact Support</a>
                <a href="#">Privacy Policy</a>
                <a href="#">Terms of Service</a>
            </div>
            <p style="margin-top: 20px; font-size: 12px; opacity: 0.8;">
                This email was sent to you because a password reset was requested for your account.<br>
                © 2025 Haruna Maiwada Community Foundation. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
        `,
      }),
    });
  
    const result = await res.json();
    if (!res.ok) {
      console.error("Brevo Error:", result);
      throw new Error("Failed to send email");
    }
    console.log("Brevo Success:", result);
    
    
  };
  