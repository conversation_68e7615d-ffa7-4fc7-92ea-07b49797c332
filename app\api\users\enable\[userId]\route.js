import { NextResponse } from "next/server"
import { dbConnect } from "@/lib/db"
import User from "@/models/User"
import { sanitizeInput } from "@/lib/sanitize"
import { rateLimit } from "@/lib/ratelimit"
import { withAdminAuth } from "@/middleware/authMiddleware"
import { createLogger } from "@/lib/logger"

// Create logger for this route
const logger = createLogger({ service: "api/users/status" });

// Create rate limiter for user status updates
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // Maximum status updates allowed
  keyPrefix: "user-status-update",
});

async function handler(req, { params }) {
  // Apply rate limiting
  const { success, response } = await limiter(req);
  if (!success) {
    logger.warn("Rate limit exceeded for user status update", { 
      admin: req.user?.email,
      userId: params.userId,
      ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown"
    });
    return response;
  }

  logger.info("User status update initiated", { 
    admin: req.user.email,
    userId: params.userId
  });

  const userId = params.userId;
  
  try {
    const rawBody = await req.json();
    
    // Sanitize input to prevent XSS attacks
    const body = sanitizeInput(rawBody);
    const { status } = body;
    
    await dbConnect();
    
    const user = await User.findById(userId);

    if (!user) {
      logger.warn("User not found during status update", {
        admin: req.user.email,
        userId: userId
      });
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }

    if (status && ["enabled", "disabled"].includes(status)) {
      user.status = status;
      await user.save();
      
      logger.info("User status updated successfully", {
        admin: req.user.email,
        userId: userId,
        username: user.username,
        newStatus: status
      });

      return NextResponse.json(
        {
          message: `User ${status} successfully`,
          user,
        },
        { status: 200 },
      );
    } else {
      logger.warn("Invalid status value provided", {
        admin: req.user.email,
        userId: userId,
        invalidStatus: status
      });
      
      return NextResponse.json(
        {
          message: "Invalid status value",
        },
        { status: 400 },
      );
    }
  } catch (error) {
    logger.error("Failed to update user status", {
      admin: req.user?.email,
      userId: userId,
      error: error.message,
      stack: error.stack
    });
    
    return NextResponse.json(
      { message: "Failed to update user", error: error.message }, 
      { status: 500 }
    );
  }
}

// Export the route handler with admin authentication middleware
export const PATCH = withAdminAuth(handler);