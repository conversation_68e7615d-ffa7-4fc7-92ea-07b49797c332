import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Search, RefreshCw } from "lucide-react";

export default function ProgramApplicantsFilters({
  search,
  setSearch,
  ward,
  setWard,
  status,
  setStatus,
  resetFilters,
  wards,
  searchInputRef
}) {
  return (
    <div className="mb-4 bg-card border border-slate-700 p-4 rounded-lg text-white">
      <h2 className="text-xl font-semibold mb-4">Filter Applicants</h2>
      
      <div className=" grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Search Input */}
        <div className="relative">
          <Search className="absolute left-2 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            ref={searchInputRef}
            placeholder="Search name, email or phone..."
            className="pl-8 bg-background  text-white border-slate-600"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
        
        {/* Ward Filter */}
        <Select value={ward} onValueChange={setWard}>
          <SelectTrigger className="bg-background w-full border-slate-600 text-accent">
            <SelectValue placeholder="Filter by ward" />
          </SelectTrigger>
          <SelectContent className="bg-card border-slate-600 text-white">
            <SelectItem value="all">All Wards</SelectItem>
            {wards.map((ward) => (
              <SelectItem key={ward} value={ward}>{ward}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        {/* Status Filter */}
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger className="bg-background w-full border-slate-600 text-accent">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent className="bg-card border-slate-600 text-white">
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
            <SelectItem value="selected">Selected</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {/* Reset Button */}
      <div className="mt-4 flex justify-end">
        <Button
          
          className="flex gap-2  bg-blue-900 hover:bg-blue-700  border-slate-600 text-white "
          onClick={resetFilters}
        >
          <RefreshCw className="h-4  text-white w-4" /> Reset Filters
        </Button>
      </div>
    </div>
  );
}