import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { dbConnect } from "@/lib/db";
import User from "@/models/User";
import Applicant from "@/models/Applicant";

export async function GET(req) {
  const session = await getServerSession(authOptions);
  if (!session || session.user.role !== "admin") {
    return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
  }

  await dbConnect();

  try {
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const search = url.searchParams.get("search") || "";

    const skip = (page - 1) * limit;

    let query = { role: "user" };
    if (search) {
      query = {
        role: "user",
        $or: [
          { name: { $regex: search, $options: "i" } },
          { username: { $regex: search, $options: "i" } },
          { phone: { $regex: search, $options: "i" } }
        ]
      };
    }

    const totalUsers = await User.countDocuments({ role: "user" });
    const totalEnabled = await User.countDocuments({ role: "user", status: "enabled" });
    const totalDisabled = await User.countDocuments({ role: "user", status: "disabled" });
    const totalSearchResults = await User.countDocuments(query);

    const users = await User.find(query)
      .select("name username phone role status createdAt new") // include `new`
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    const usersWithApplicantCount = await Promise.all(
      users.map(async (user) => {
        const applicantCount = await Applicant.countDocuments({ reviewerId: user._id });

        return {
          ...user,
          applicantCount,
          ...(user.new === true && { password: "123456" }), // add password only if new === true
        };
      })
    );

    const totalPages = Math.ceil(totalSearchResults / limit);

    return NextResponse.json(
      {
        totalUsers,
        totalEnabled,
        totalDisabled,
        users: usersWithApplicantCount,
        currentPage: page,
        totalPages,
        limit,
        totalSearchResults,
      },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json(
      { message: "Failed to fetch users", error: error.message },
      { status: 500 }
    );
  }
}