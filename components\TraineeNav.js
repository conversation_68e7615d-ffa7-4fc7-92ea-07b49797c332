"use client";

import Link from "next/link";
import { LayoutDashboard, ClipboardList, History, CheckCircle, User, Bell, File } from "lucide-react";
import TraineeOnly from "./TraineeOnly";

export default function UserNav() {
  return (
    <TraineeOnly>
      <ul className="space-y-2">
        <li>
          <Link
            href="/dashboard"
            className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-800"
          >
            <LayoutDashboard size={20} /> Dashboard
          </Link>
        </li>
        <li>
          <Link
            href="/managefiles"
            className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-800"
          >
            <File size={20} /> Manage Files
          </Link>
        </li>
        <li>
          <Link
            href="/profile"
            className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-800"
          >
            <User size={20} /> Profile
          </Link>
        </li>
        <li>
          <Link
            href="/notifications"
            className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-800"
          >
            <Bell size={20} /> Notifications
          </Link>
        </li>
      </ul>
    </TraineeOnly>
  );
}
