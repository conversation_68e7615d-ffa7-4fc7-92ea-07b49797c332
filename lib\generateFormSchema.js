import { z } from "zod";

// Function to generate a Zod schema dynamically
export function generateFormSchema(fields) {
  const schemaShape = {};

  fields.forEach((field) => {
    switch (field.inputType) {
      case "text":
        schemaShape[field.label] = z
          .string()
          .min(1, `${field.label} is required`)
          .max(60, `${field.label} too long`);
        break;
      case "tel":
        schemaShape[field.label] = z
          .string()
          .min(9, `${field.label} must be at least 9 characters`)
          .max(15, `${field.label} too long`);
        break;
      case "email":
        schemaShape[field.label] = z
          .string()
          .email("Invalid email address")
          .max(50, `${field.label} too long`);
        break;
      case "number":
        schemaShape[field.label] = z
          .number({ invalid_type_error: `${field.label} must be a number` });
        break;
      case "date":
        schemaShape[field.label] = z
          .string()
          .refine((val) => !isNaN(Date.parse(val)), `${field.label} must be a valid date`);
        break;
      case "file":
        schemaShape[field.label] = z
          .instanceof(File, { message: "A valid file is required" })
          .refine((file) => file.size <= 2 * 1024 * 1024, `File size must be less than 2MB`);
        break;
      default:
        schemaShape[field.label] = z.string().optional();
    }

    if (field.required) {
      schemaShape[field.label] = schemaShape[field.label].refine(
        (val) => val !== undefined && val !== "",
        `${field.label} is required`
      );
    }
  });

  return z.object(schemaShape);
}