const mongoose = require('mongoose');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/haruna';

// Main function to add unique index and clean duplicates
async function addUniquePhoneIndex() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully!');

    const db = mongoose.connection.db;
    const collection = db.collection('applicants');

    console.log('🔍 Checking for existing duplicates...');
    
    // Find duplicates using aggregation
    const duplicates = await collection.aggregate([
      {
        $match: {
          "formData.phonenumber": { $exists: true, $ne: null, $ne: "" }
        }
      },
      {
        $group: {
          _id: {
            programId: "$programId",
            phone: "$formData.phonenumber"
          },
          docs: { $push: "$_id" },
          count: { $sum: 1 }
        }
      },
      {
        $match: {
          count: { $gt: 1 }
        }
      }
    ]).toArray();

    console.log(`📊 Found ${duplicates.length} sets of duplicate phone numbers`);

    if (duplicates.length > 0) {
      console.log('🧹 Cleaning up duplicates (keeping the oldest entry for each phone)...');
      
      let totalRemoved = 0;
      for (const duplicate of duplicates) {
        // Keep the first document (oldest) and remove the rest
        const docsToRemove = duplicate.docs.slice(1);
        
        if (docsToRemove.length > 0) {
          const result = await collection.deleteMany({
            _id: { $in: docsToRemove }
          });
          
          totalRemoved += result.deletedCount;
          console.log(`  📱 Phone ${duplicate._id.phone} in program ${duplicate._id.programId}: removed ${result.deletedCount} duplicates`);
        }
      }
      
      console.log(`✅ Removed ${totalRemoved} duplicate entries`);
    }

    console.log('📋 Checking for existing indexes...');
    const existingIndexes = await collection.indexes();
    const hasUniqueIndex = existingIndexes.some(index => 
      index.name === 'unique_phone_per_program'
    );

    if (hasUniqueIndex) {
      console.log('ℹ️  Unique phone index already exists');
    } else {
      console.log('🔧 Creating unique compound index...');
      
      try {
        await collection.createIndex(
          { 
            programId: 1, 
            "formData.phonenumber": 1 
          }, 
          { 
            unique: true,
            name: "unique_phone_per_program",
            partialFilterExpression: { 
              "formData.phonenumber": { $exists: true, $ne: null, $ne: "" } 
            }
          }
        );
        
        console.log('✅ Unique compound index created successfully!');
      } catch (indexError) {
        if (indexError.code === 11000) {
          console.error('❌ Failed to create index due to remaining duplicates');
          console.error('   Please run the script again or manually clean duplicates');
        } else {
          console.error('❌ Failed to create index:', indexError.message);
        }
        throw indexError;
      }
    }

    // Verify the index
    console.log('🔍 Verifying index creation...');
    const finalIndexes = await collection.indexes();
    const uniqueIndex = finalIndexes.find(index => index.name === 'unique_phone_per_program');
    
    if (uniqueIndex) {
      console.log('✅ Index verification successful!');
      console.log('📋 Index details:', {
        name: uniqueIndex.name,
        key: uniqueIndex.key,
        unique: uniqueIndex.unique,
        partialFilterExpression: uniqueIndex.partialFilterExpression
      });
    } else {
      console.error('❌ Index verification failed - index not found');
    }

    // Final statistics
    const totalApplicants = await collection.countDocuments();
    const applicantsWithPhone = await collection.countDocuments({
      "formData.phonenumber": { $exists: true, $ne: null, $ne: "" }
    });
    
    console.log('\n📊 Final Statistics:');
    console.log(`   Total applicants: ${totalApplicants}`);
    console.log(`   Applicants with phone numbers: ${applicantsWithPhone}`);
    console.log(`   Duplicates removed: ${totalRemoved}`);

  } catch (error) {
    console.error('❌ Error during migration:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  console.log('🚀 Starting unique phone index migration...\n');
  addUniquePhoneIndex()
    .then(() => {
      console.log('\n🎉 Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = { addUniquePhoneIndex };
