"use client"
import AuthOnly from "@/components/AuthOnly"
import { useParams } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import DrawerLayout from "@/components/DrawerLayout"
import { FileText, User, Calendar, Building, AlertCircle } from "lucide-react"
import ApplicantActionButtons from "@/components/ApplicantActionButtons"
import axios from "axios"
import { useQuery } from "@tanstack/react-query"
import BackButton from "@/components/Back-Button"
const defaultFields = ["name", "gender", "phonenumber", "ward", "state"]

const getBadgeVariant = (status) => {
  switch (status) {
    case "approved":
      return "border-lime-600 bg-background w-20 text-lime-600"
    case "pending":
      return "border-amber-500 bg-background w-20 text-amber-500"
    case "rejected":
      return "border-rose-600 bg-background w-20 text-rose-600"
    case "selected":
      return "border-blue-500 bg-background w-20 text-blue-500"
    default:
      return "border-slate-500 bg-background w-20 text-slate-500"
  }
}

// Fetch function using axios
const fetchApplicant = async (id) => {
  const response = await axios.get(`/api/applicants/fulldetails/${id}`)
  return response.data
}

export default function ApplicantDetailsPage() {
  const params = useParams()
  const { id } = params

  // Using React Query to fetch and manage data
  const {
    data: applicant,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["applicant", id],
    queryFn: () => fetchApplicant(id),
    enabled: !!id, // Only run the query if we have an ID
    staleTime: 1000 * 60 * 5, // Consider data fresh for 5 minutes
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
  })

  if (isLoading) {
    return (
      <AuthOnly>
        <DrawerLayout>
          <div className="w-full mx-auto px-4 mb-8">
            <BackButton />
            <div className="flex flex-col justify-between items-center px-5 mb-6">
              <div className="h-8 w-48 bg-slate-700/30 animate-pulse rounded-md mt-3 mb-4"></div>
              <div className="flex items-center gap-2">
                <div className="h-5 w-20 bg-slate-700/30 animate-pulse rounded-md"></div>
                <div className="h-6 w-20 bg-slate-700/30 animate-pulse rounded-full"></div>
              </div>
            </div>

            {/* Skeleton for bento grid layout */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {/* Main info card skeleton */}
              <Card className="md:col-span-2">
                <CardHeader>
                  <div className="h-6 w-40 bg-slate-700/30 animate-pulse rounded-md"></div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="space-y-2 border border-slate-700 bg-background rounded-lg p-3 shadow-sm">
                        <div className="h-4 w-24 bg-slate-700/30 animate-pulse rounded-md"></div>
                        <div className="h-5 w-32 bg-slate-700/30 animate-pulse rounded-md"></div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Program & Reviewer info skeleton */}
              <Card>
                <CardHeader>
                  <div className="h-6 w-40 bg-slate-700/30 animate-pulse rounded-md"></div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="space-y-2 border border-slate-700 bg-background rounded-lg p-3 shadow-sm">
                      <div className="h-4 w-24 bg-slate-700/30 animate-pulse rounded-md"></div>
                      <div className="h-5 w-32 bg-slate-700/30 animate-pulse rounded-md"></div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Additional info skeleton */}
              <Card className="md:col-span-2">
                <CardHeader>
                  <div className="h-6 w-40 bg-slate-700/30 animate-pulse rounded-md"></div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="space-y-2 border border-slate-700 bg-background rounded-lg p-3 shadow-sm">
                        <div className="h-4 w-24 bg-slate-700/30 animate-pulse rounded-md"></div>
                        <div className="h-5 w-32 bg-slate-700/30 animate-pulse rounded-md"></div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Files skeleton */}
              <Card>
                <CardHeader>
                  <div className="h-6 w-40 bg-slate-700/30 animate-pulse rounded-md"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div
                        key={i}
                        className="flex items-center justify-between bg-background border border-slate-700 rounded-lg p-3 shadow-sm"
                      >
                        <div className="h-4 w-24 bg-slate-700/30 animate-pulse rounded-md"></div>
                        <div className="h-8 w-16 bg-slate-700/30 animate-pulse rounded-md"></div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Actions skeleton */}
            <Card className="flex flex-col items-center justify-center p-4 border border-slate-700">
              <div className="h-6 w-40 bg-slate-700/30 animate-pulse rounded-md mb-4"></div>
              <div className="flex gap-2">
                <div className="h-10 w-24 bg-slate-700/30 animate-pulse rounded-md"></div>
                <div className="h-10 w-24 bg-slate-700/30 animate-pulse rounded-md"></div>
              </div>
            </Card>
          </div>
        </DrawerLayout>
      </AuthOnly>
    )
  }

  if (isError) {
    return (
      <div className="flex flex-col justify-center items-center h-screen">
        <AlertCircle className="h-8 w-8 text-rose-600 mb-4" />
        <p className="text-rose-600 font-medium">Failed to load applicant details</p>
        <p className="text-muted-foreground mt-2">{error.message || "An error occurred"}</p>
      </div>
    )
  }

  if (!applicant) {
    return (
      <div className="flex flex-col justify-center items-center h-screen">
        <AlertCircle className="h-8 w-8 text-amber-500 mb-4" />
        <p className="text-amber-500 font-medium">Applicant not found</p>
      </div>
    )
  }

  const formData = applicant.formData || {}

  const grouped = {
    default: {},
    additional: {},
    files: {},
  }

  Object.entries(formData).forEach(([key, value]) => {
    if (defaultFields.includes(key)) {
      grouped.default[key] = value
    } else if (
      (typeof value === "string" && value.startsWith("http://")) ||
      (typeof value === "string" && value.startsWith("https://")) ||
      (typeof value === "object" && value !== null && value.url?.startsWith("http://")) ||
      (typeof value === "object" && value !== null && value.url?.startsWith("https://"))
    ) {
      grouped.files[key] = typeof value === "string" ? value : value.url
    } else {
      grouped.additional[key] = value
    }
  })

  return (
    <AuthOnly>
      <DrawerLayout>
        <div className="w-full mx-auto px-4 mb-8 ">
          {/* Header with status */}
          <BackButton />
          <div className="flex flex-col justify-between items-center px-5 mb-6">
            <h1 className=" text-lg md:text-2xl font-bold mt-3 md:mt-0 mb-4">Applicant Details</h1>
            <div className="flex items-center gap-2">
              <p className="text-sm font-medium text-muted-foreground">Status:</p>
              <Badge className={getBadgeVariant(applicant.status)}>{applicant.status}</Badge>
            </div>
          </div>

          {/* Bento grid layout */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {/* Main info card - spans 2 columns */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="flex text-white items-center gap-2">
                  <User className="h-5 w-5" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {Object.entries(grouped.default).map(([key, value]) => (
                    <div key={key} className="space-y-1 border border-slate-700 bg-background rounded-lg p-3 shadow-sm">
                      <p className="text-sm max-w-full line-clamp-3 break-words font-medium text-muted-foreground capitalize">
                        {key}
                      </p>
                      {value ? (
                        <p className="font-medium max-w-full line-clamp-3 break-words ">{value}</p>
                      ) : (
                        <p className="text-sm italic text-muted-foreground flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" /> Not provided
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Program & Reviewer info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex text-white  items-center gap-2">
                  <Building className="h-5 w-5" />
                  Program/Reviewer Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-1 border bg-background border-slate-700 rounded-lg p-3 shadow-sm">
                  <p className="text-sm font-medium text-muted-foreground">Program</p>
                  {applicant.programName ? (
                    <p className="font-medium max-w-full break-words line-clamp-3">{applicant.programName}</p>
                  ) : (
                    <p className="text-sm italic text-muted-foreground flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" /> Not specified
                    </p>
                  )}
                </div>
                <div className="space-y-1 border border-slate-700 bg-background rounded-lg p-3 shadow-sm">
                  <p className="text-sm font-medium text-muted-foreground">Reviewed By</p>
                  {applicant.reviewer ? (
                    <p className="font-medium max-w-full break-words  line-clamp-3">{`${applicant.reviewer.name} (${applicant.reviewer.username})`}</p>
                  ) : (
                    <p className="text-sm italic text-muted-foreground flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" /> Not reviewed yet
                    </p>
                  )}
                </div>
                <div className="space-y-1 border border-slate-700 bg-background rounded-lg p-3 shadow-sm">
                  <p className="text-sm font-medium text-muted-foreground">Applied on</p>
                  {applicant.createdAt ? (
                    <p className="font-medium">{new Date(applicant.createdAt).toLocaleString()}</p>
                  ) : (
                    <p className="text-sm italic text-muted-foreground flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" /> Date not available
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Additional info */}
            {Object.keys(grouped.additional).length > 0 && (
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle className="flex text-white  items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Additional Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {Object.entries(grouped.additional)
                      .filter(([key]) => key !== "programid") // exclude programid
                      .map(([key, value]) => (
                        <div
                          key={key}
                          className="space-y-1 border border-slate-700 bg-background rounded-lg p-3 shadow-sm"
                        >
                          <p className="text-sm font-medium max-w-full break-words  line-clamp-3 text-muted-foreground capitalize">
                            {key}
                          </p>
                          {value ? (
                            <p className="font-medium max-w-full line-clamp-3">{String(value)}</p>
                          ) : (
                            <p className="text-sm italic text-muted-foreground flex items-center gap-1">
                              <AlertCircle className="h-3 w-3" /> Not provided
                            </p>
                          )}
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Files */}
            {Object.keys(grouped.files).length > 0 && (
              <Card className="md:col-span-1">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Uploaded Files
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(grouped.files).map(([key, url]) => (
                      <div
                        key={key}
                        className="flex items-center justify-between bg-background border border-slate-700 rounded-lg p-3 shadow-sm"
                      >
                        <p className="text-sm font-medium capitalize max-w-full break-words  line-clamp-3">{key}</p>
                        {url ? (
                          <Button asChild variant="outline" size="sm">
                            <a href={url} target="_blank" rel="noopener noreferrer">
                              View
                            </a>
                          </Button>
                        ) : (
                          <p className="text-sm italic text-muted-foreground flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" /> No file
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
          {applicant.status !== "selected" && (
            <Card className="flex flex-col items-center justify-center p-4 border border-slate-700">
              <h2 className="text-center text-white  font-bold flex flex-row">Application Actions</h2>
              <CardFooter>
                <ApplicantActionButtons applicantId={id} programid={applicant.programId} />
              </CardFooter>
            </Card>
          )}
        </div>
      </DrawerLayout>
    </AuthOnly>
  )
}
