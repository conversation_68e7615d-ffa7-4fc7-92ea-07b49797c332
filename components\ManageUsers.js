"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import axios from "axios"
import { toast } from "sonner"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import {
  Users,
  RefreshCcw,
  UserCheck,
  UserX,
  Search,
  ChevronLeft,
  ChevronRight,
  Copy,
  MoreHorizontal,
} from "lucide-react"
import { formatDate } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

// Helper function to get badge styling based on status
const getBadgeVariant = (status) => {
  switch (status) {
    case "enabled":
      return "border-lime-600 text-lime-600"
    case "disabled":
      return "border-rose-600 text-rose-600"
    default:
      return "border-slate-500 text-slate-500"
  }
}

// Skeleton row component for loading state
function SkeletonRow() {
  return (
    <TableRow className="border-b-slate-700 text-center">
      <TableCell>
        <div className="h-4 w-28 bg-slate-700 rounded animate-pulse mx-auto"></div>
      </TableCell>
      <TableCell>
        <div className="h-4 w-24 bg-slate-700 rounded animate-pulse mx-auto"></div>
      </TableCell>
      <TableCell>
        <div className="h-4 w-24 bg-slate-700 rounded animate-pulse mx-auto"></div>
      </TableCell>
      <TableCell>
        <div className="h-4 w-10 bg-slate-700 rounded animate-pulse mx-auto"></div>
      </TableCell>
      <TableCell>
        <div className="h-6 w-20 bg-slate-700 rounded-full animate-pulse mx-auto"></div>
      </TableCell>
      <TableCell>
        <div className="h-4 w-24 bg-slate-700 rounded animate-pulse mx-auto"></div>
      </TableCell>
      <TableCell>
        <div className="h-8 w-8 bg-slate-700 rounded animate-pulse mx-auto"></div>
      </TableCell>
    </TableRow>
  )
}

export default function UsersPage() {
  const [search, setSearch] = useState("")
  const [debouncedSearch, setDebouncedSearch] = useState("")
  const [page, setPage] = useState(1)
  const limit = 10

  // Debounce search input
  const handleSearchChange = (e) => {
    setSearch(e.target.value)
    setTimeout(() => {
      setDebouncedSearch(e.target.value)
      setPage(1) // Reset to first page on new search
    }, 500)
  }

  // Fetch users with React Query
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["users", page, limit, debouncedSearch],
    queryFn: async () => {
      const response = await axios.get(`/api/users/fetch?page=${page}&limit=${limit}&search=${debouncedSearch}`)
      return response.data
    },
  })

  // Handle user status toggle
  const toggleUserStatus = async (userId, currentStatus) => {
    const newStatus = currentStatus === "enabled" ? "disabled" : "enabled"
    try {
      await axios.patch(`/api/users/enable/${userId}`, { status: newStatus })
      toast.success(`User ${newStatus} successfully`)
      refetch() // Refresh data after update
    } catch (error) {
      toast.error("Failed to update user status")
      console.error(error)
    }
  }

  // Copy user credentials to clipboard
  const copyCredentials = (username, password) => {
    try {
      const credentials = `Congratulations you have been assigned a role at (Haruna Maiwada Community Foundation), Use the following credentials to log into your account and update your profile.  Username: ${username}\nPassword: ${password}`
      navigator.clipboard
        .writeText(credentials)
        .then(() => {
          toast.success("Credentials copied to clipboard")
        })
        .catch(() => {
          // Fallback for browsers that don't support clipboard API
          fallbackCopyCredentials(username, password)
        })
    } catch (err) {
      fallbackCopyCredentials(username, password)
    }
  }

  // Fallback copy method for older browsers
  const fallbackCopyCredentials = (username, password) => {
    try {
      // Create a temporary textarea element
      const textArea = document.createElement("textarea")
      const credentials =  `Congratulations you have been assigned a role at (Haruna Maiwada Community Foundation), Use the following credentials to log into your account and update your profile.  Username: ${username}\nPassword: ${password}`

      // Set the value and styles
      textArea.value = credentials
      textArea.style.position = "fixed" // Make it invisible
      textArea.style.left = "-999999px"
      textArea.style.top = "-999999px"

      // Add to DOM, select all text, and execute copy command
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      const successful = document.execCommand("copy")
      document.body.removeChild(textArea)

      if (successful) {
        toast.success("Credentials copied to clipboard")
      } else {
        toast.error("Failed to copy credentials")
      }
    } catch (err) {
      toast.error("Failed to copy credentials")
      console.error("Copy failed: ", err)
    }
  }

  // Generate pagination buttons
  const renderPaginationButtons = () => {
    if (!data) return null

    const buttons = []
    const totalPages = data.totalPages

    // Show at most 5 page links
    const startPage = Math.max(1, page - 2)
    const endPage = Math.min(totalPages, startPage + 4)

    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <Button
          key={i}
          variant={page === i ? "default" : "outline"}
          size="icon"
          className="w-9 h-9"
          onClick={() => setPage(i)}
        >
          {i}
        </Button>,
      )
    }

    return buttons
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatsCard
          title="Total Users"
          value={data?.totalUsers || 0}
          icon={<Users className="h-8 w-8" />}
          className="bg-card text-white"
        />
        <StatsCard
          title="Enabled Users"
          value={data?.totalEnabled || 0}
          icon={<UserCheck className="h-8 text-lime-600 w-8" />}
          className="bg-card text-white"
        />
        <StatsCard
          title="Disabled Users"
          value={data?.totalDisabled || 0}
          icon={<UserX className="h-8 text-red-700 w-8" />}
          className="bg-card text-white"
        />
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search users by name, username or phone..."
          value={search}
          onChange={handleSearchChange}
          className="pl-10 border border-slate-700"
        />
      </div>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <h1 className="text-center font-bold text-white font-mono">Users</h1>
        </CardHeader>
        <CardContent>
          <Table className="text-white">
            <TableHeader className="text-center">
              <TableRow className="text-center bg-slate-800 border-b-slate-700 rounded-2xl text-white font-bold">
                <TableHead className="text-center text-white font-bold">Name</TableHead>
                <TableHead className="text-center text-white font-bold">Username</TableHead>
                <TableHead className="text-center text-white font-bold">Phone</TableHead>
                <TableHead className="text-center text-white font-bold">Applicants reviewed</TableHead>
                <TableHead className="text-center text-white font-bold">Status</TableHead>
                <TableHead className="text-center text-white font-bold">Added On</TableHead>
                <TableHead className="text-center text-white font-bold">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                // Render skeleton rows when loading
                Array(5).fill(0).map((_, index) => (
                  <SkeletonRow key={`skeleton-${index}`} />
                ))
              ) : data?.users?.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    No users found
                  </TableCell>
                </TableRow>
              ) : (
                data?.users?.map((user) => (
                  <TableRow key={user._id} className="border-b-slate-700 text-center">
                    <TableCell className="font-medium">{user.name}</TableCell>
                    <TableCell>{user.username}</TableCell>
                    <TableCell className="text-center">{user.phone || "N/A"}</TableCell>
                    <TableCell className="">{user.applicantCount || "0"}</TableCell>
                    <TableCell className="">
                      <Badge variant="outline" className={`${getBadgeVariant(user.status)} border`}>
                        {user.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="">{formatDate(user.createdAt)}</TableCell>
                    <TableCell>
                      <div className="flex justify-center items-center">
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button size="icon" className="h-8 bg-slate-800 border-2 border-slate-700  w-8">
                              <MoreHorizontal className="h-4 text-white w-4" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-56 p-2 bg-black border border-slate-700" align="end">
                            <div className="flex flex-col">
                              <Button
                                size="sm"
                                className={user.status === "enabled" ? "bg-red-900 hover:bg-red-600 text-white" : "bg-green-900 hover:bg-green-600 text-white"}
                                onClick={() => toggleUserStatus(user._id, user.status)}
                              >
                                {user.status === "enabled" ? " ❌ Disable User" : "✅ Enable User"}
                              </Button>

                              {/* Copy Credentials Button - Only shown if password exists */}
                              {user.password && (
                                <Button
                                  size="sm"
                                  className="justify-start mt-1 text-white rounded-md bg-blue-900 hover:bg-blue-600"
                                  onClick={() => copyCredentials(user.username, user.password)}
                                >
                                  <Copy className="h-4 text-white w-4 mr-2" /> Copy Login Details
                                </Button>
                              )}
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* Pagination with Buttons */}
          {data?.totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2 mt-4">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setPage((p) => Math.max(1, p - 1))}
                disabled={page === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              {renderPaginationButtons()}

              <Button
                variant="outline"
                size="icon"
                onClick={() => setPage((p) => Math.min(data.totalPages, p + 1))}
                disabled={page === data.totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// Stats Card Component
function StatsCard({ title, value, icon, className }) {
  return (
    <Card className={className}>
      <CardContent className="flex items-center  justify-between ">
        <div>
          <p className="text-sm font-medium text-white ">{title}</p>
          <p className="text-3xl font-bold">{value}</p>
        </div>
        <div className="rounded-full p-3 bg-slate-950 border-2 border-slate-700">{icon}</div>
      </CardContent>
    </Card>
  )
}