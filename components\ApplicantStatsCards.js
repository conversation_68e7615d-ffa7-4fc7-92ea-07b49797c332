"use client";
import { Card, CardContent } from "@/components/ui/card";
import { Users, CheckCircle, Briefcase, ScanText } from "lucide-react";
import CountUp from "react-countup";

export default function ApplicantStatsCards({ totalApplicants, filteredTotal, programTotal }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <Card className="bg-card border  border-slate-700 text-white">
        <CardContent className="pt-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium ">Applicants </p>
              <h3 className="text-2xl font-bold">
                Stats
              </h3>
            </div>
            <ScanText className="h-8 w-8 " />
          </div>
        </CardContent>
      </Card>
      <Card className="bg-card border border-slate-700 text-white">
        <CardContent className="pt-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium ">Total Applicants</p>
              <h3 className="text-2xl font-bold">
             {totalApplicants || 0}
              </h3>
            </div>
            <Users className="h-8 w-8 " />
          </div>
        </CardContent>
      </Card>

      <Card className="bg-card border border-slate-700 text-white">
        <CardContent className="pt-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium ">Showing Results</p>
              <h3 className="text-2xl font-bold">
                <CountUp end={filteredTotal || 0} duration={1.5} separator="," />
              </h3>
            </div>
            <CheckCircle className="h-8 w-8 " />
          </div>
        </CardContent>
      </Card>

      
    </div>
  );
}