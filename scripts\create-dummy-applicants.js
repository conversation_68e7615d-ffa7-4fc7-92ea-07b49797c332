const mongoose = require('mongoose');

// Define wards directly since we can't import ES6 modules in CommonJS
const wards = [
  "WAKILIN GABAS I",
  "WAKILIN GABAS II",
  "KANGI<PERSON>",
  "WAKILIN YAMMA I",
  "WAKILIN YAMMA II",
  "WAKILIN KUDU I",
  "WAKILIN KUDU II",
  "WAKILIN AREWA (A)",
  "WAKILIN AREWA (B)",
  "SHINKAFI 'A'",
  "SHINKAFI 'B'",
  "WAKILI KUDU III",
];

// MongoDB connection string - update this to match your database
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/haruna';

// Applicant Schema (matching your model)
const ApplicantSchema = new mongoose.Schema(
  {
    programId: {
      type: String,
      required: true,
    },
    formPhase: {
      type: Number,
      required: true,
    },
    formData: {
      type: Map,
      of: mongoose.Schema.Types.Mixed,
      required: true,
    },
    status: {
      type: String,
      enum: ["pending", "approved", "rejected", "selected"],
      default: "pending",
    },
    reviewerId: {
      type: String,
      default: null,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  { timestamps: true }
);

const Applicant = mongoose.models.Applicant || mongoose.model('Applicant', ApplicantSchema);

// Sample data arrays
const firstNames = [
  'Abubakar', 'Fatima', 'Ibrahim', 'Aisha', 'Muhammad', 'Zainab', 'Usman', 'Khadija',
  'Ahmad', 'Hauwa', 'Yusuf', 'Maryam', 'Abdullahi', 'Safiya', 'Ismail', 'Hadiza',
  'Suleiman', 'Amina', 'Bashir', 'Rukayya', 'Garba', 'Salamatu', 'Musa', 'Balkisu',
  'Aliyu', 'Zara', 'Haruna', 'Nafisa', 'Kabir', 'Jamila', 'Sani', 'Rabi',
  'Adamu', 'Asma', 'Lawal', 'Halima', 'Nasir', 'Kaltum', 'Tijani', 'Maimuna',
  'Rabiu', 'Sadiya', 'Yakubu', 'Bilkisu', 'Shehu', 'Zahra', 'Nuhu', 'Habiba'
];

const lastNames = [
  'Abdullahi', 'Muhammad', 'Ibrahim', 'Abubakar', 'Usman', 'Ahmad', 'Yusuf', 'Suleiman',
  'Bashir', 'Garba', 'Musa', 'Aliyu', 'Haruna', 'Kabir', 'Sani', 'Adamu',
  'Lawal', 'Nasir', 'Tijani', 'Rabiu', 'Yakubu', 'Shehu', 'Nuhu', 'Ismail',
  'Mamman', 'Bello', 'Danjuma', 'Salisu', 'Aminu', 'Hamza', 'Sadiq', 'Faruk',
  'Khalil', 'Jamil', 'Nura', 'Safwan', 'Marwan', 'Bilal', 'Omar', 'Zakariya'
];

const occupations = [
  'Farmer', 'Trader', 'Teacher', 'Tailor', 'Driver', 'Mechanic', 'Carpenter', 'Barber',
  'Welder', 'Mason', 'Electrician', 'Plumber', 'Shopkeeper', 'Food Vendor', 'Cleaner',
  'Security Guard', 'Student', 'Housewife', 'Artisan', 'Blacksmith', 'Potter', 'Fisherman'
];

const educationLevels = [
  'No Formal Education', 'Primary School', 'Secondary School', 'Diploma', 'Degree', 'Quranic Education'
];

// Generate random phone number
function generatePhoneNumber() {
  const prefixes = ['0803', '0806', '0813', '0816', '0703', '0706', '0708', '0802', '0808', '0812'];
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const suffix = Math.floor(Math.random() * 10000000).toString().padStart(7, '0');
  return prefix + suffix;
}

// Generate random email
function generateEmail(firstName, lastName) {
  const domains = ['gmail.com', 'yahoo.com', 'hotmail.com'];
  const domain = domains[Math.floor(Math.random() * domains.length)];
  const number = Math.floor(Math.random() * 999);
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}${number}@${domain}`;
}

// Generate random age between 18 and 65
function generateAge() {
  return Math.floor(Math.random() * (65 - 18 + 1)) + 18;
}

// Generate program ID - set your specific program ID here
function generateProgramId() {
  // Replace this with your actual program ID
  return '680acb7b1d550cd5912788cb';
}

// Generate random status with weighted distribution (no pre-selected applicants)
function generateStatus() {
  const rand = Math.random();
  if (rand < 0.65) return 'approved';  // 65% approved
  if (rand < 0.85) return 'pending';   // 20% pending
  return 'rejected';                   // 15% rejected
  // Note: 'selected' status should only be set by the selection process, not randomly
}

// Create a single dummy applicant
function createDummyApplicant() {
  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
  const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
  const ward = wards[Math.floor(Math.random() * wards.length)];
  
  return {
    programId: `68ae5f85880002eb4246df3b`,
    formPhase: Math.floor(Math.random() * 3) + 1, // 1, 2, or 3
    formData: new Map([
      ['name', `${firstName} ${lastName}`],
      ['gender', Math.random() > 0.5 ? 'Male' : 'Female'],
      ['email', generateEmail(firstName, lastName)],
      ['phonenumber', generatePhoneNumber()],
      ['age', generateAge().toString()],
      ['ward', ward],
      ['state', 'Katsina'],
      ['occupation', occupations[Math.floor(Math.random() * occupations.length)]],
      ['education', educationLevels[Math.floor(Math.random() * educationLevels.length)]],
      ['maritalstatus', Math.random() > 0.5 ? 'Married' : 'Single'],
      ['householdsize', Math.floor(Math.random() * 10) + 1],
      ['monthlyincome', Math.floor(Math.random() * 50000) + 10000]
    ]),
    status: generateStatus(),
    reviewerId: Math.random() > 0.7 ? 'reviewer_' + Math.floor(Math.random() * 5) : null,
    createdAt: new Date(Date.now() - Math.floor(Math.random() * 90 * 24 * 60 * 60 * 1000)) // Random date within last 90 days
  };
}

// Main function to create and insert dummy applicants
async function createDummyApplicants() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully!');

    console.log('Creating 1000 dummy applicants...');
    
    const applicants = [];
    for (let i = 0; i < 1000; i++) {
      applicants.push(createDummyApplicant());
      
      // Show progress every 100 applicants
      if ((i + 1) % 100 === 0) {
        console.log(`Generated ${i + 1} applicants...`);
      }
    }

    console.log('Inserting applicants into database...');
    const result = await Applicant.insertMany(applicants);
    
    console.log(`✅ Successfully created ${result.length} dummy applicants!`);
    
    // Show some statistics
    const statusCounts = {};
    const wardCounts = {};
    
    applicants.forEach(applicant => {
      statusCounts[applicant.status] = (statusCounts[applicant.status] || 0) + 1;
      const ward = applicant.formData.get('ward');
      wardCounts[ward] = (wardCounts[ward] || 0) + 1;
    });
    
    console.log('\n📊 Statistics:');
    console.log('Status distribution:', statusCounts);
    console.log('Ward distribution (top 5):');
    Object.entries(wardCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .forEach(([ward, count]) => console.log(`  ${ward}: ${count}`));

  } catch (error) {
    console.error('❌ Error creating dummy applicants:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  createDummyApplicants();
}

module.exports = { createDummyApplicants };
