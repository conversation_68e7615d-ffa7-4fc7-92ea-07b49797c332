import mongoose from "mongoose";

const ProgramSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    linkName: { type: String, unique: true }, // Slugified name for form link
    description: { type: String, required: true },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    venue: { type: String, required: true }, // ✅ Added venue field
    status: {
      type: String,
      enum: ["upcoming", "ongoing", "completed"],
      default: "upcoming",
    },
    maxBeneficiaries: { type: Number, required: true },
    formId: { type: String, default: null },
    selectionProcess: { type: Boolean, default: false }, // ✅ New field added
  },
  { timestamps: true }
);

export default mongoose.models.Program || mongoose.model("Program", ProgramSchema);
