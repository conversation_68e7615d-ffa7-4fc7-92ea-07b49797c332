{"level":"error","message":"MongoDB connection failed","metadata":{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","service":"database-connection","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async dbConnect (webpack-internal:///(rsc)/./lib/db.js:19:9)\n    at async Object.authorize (webpack-internal:///(rsc)/./app/api/auth/[...nextauth]/route.js:87:21)\n    at async Object.callback (webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/callback.js:328:14)\n    at async AuthHandler (webpack-internal:///(rsc)/./node_modules/next-auth/core/index.js:260:28)\n    at async NextAuthRouteHandler (webpack-internal:///(rsc)/./node_modules/next-auth/next/index.js:57:28)\n    at async NextAuth._args$ (webpack-internal:///(rsc)/./node_modules/next-auth/next/index.js:89:16)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\compiled\\next-server\\app-route.runtime.dev.js:26:33891)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\compiled\\next-server\\app-route.runtime.dev.js:26:41254)\n    at async doRender (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:2393:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:2430:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:1003:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\next-server.js:304:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:895:17)\n    at async C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\router-server.js:235:21)\n    at async handleRequest (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\router-server.js:426:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\router-server.js:450:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)"},"timestamp":"2025-05-20 23:13:47"}
{"level":"error","message":"Database connection failed","metadata":{"error":"Database connection failed","ip":"::ffff:************","service":"auth","stack":"Error: Database connection failed\n    at dbConnect (webpack-internal:///(rsc)/./lib/db.js:26:15)\n    at async Object.authorize (webpack-internal:///(rsc)/./app/api/auth/[...nextauth]/route.js:87:21)\n    at async Object.callback (webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/callback.js:328:14)\n    at async AuthHandler (webpack-internal:///(rsc)/./node_modules/next-auth/core/index.js:260:28)\n    at async NextAuthRouteHandler (webpack-internal:///(rsc)/./node_modules/next-auth/next/index.js:57:28)\n    at async NextAuth._args$ (webpack-internal:///(rsc)/./node_modules/next-auth/next/index.js:89:16)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\compiled\\next-server\\app-route.runtime.dev.js:26:33891)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\compiled\\next-server\\app-route.runtime.dev.js:26:41254)\n    at async doRender (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:2393:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:2430:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:1003:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\next-server.js:304:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:895:17)\n    at async C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\router-server.js:235:21)\n    at async handleRequest (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\router-server.js:426:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\router-server.js:450:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)","username":"<EMAIL>"},"timestamp":"2025-05-20 23:13:47"}
{"level":"error","message":"MongoDB connection failed","metadata":{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","service":"database-connection","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async dbConnect (webpack-internal:///(rsc)/./lib/db.js:19:9)\n    at async GET (webpack-internal:///(rsc)/./app/api/applicants/ward-selection-stats/route.js:14:5)\n    at async AppRouteRouteModule.do (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\compiled\\next-server\\app-route.runtime.dev.js:26:33891)\n    at async AppRouteRouteModule.handle (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\compiled\\next-server\\app-route.runtime.dev.js:26:41254)\n    at async doRender (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:1915:28)\n    at async DevServer.renderPageComponent (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:2393:24)\n    at async DevServer.renderToResponseImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:2430:32)\n    at async DevServer.pipeImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:1003:25)\n    at async NextNodeServer.handleCatchallRenderRequest (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\next-server.js:304:17)\n    at async DevServer.handleRequestImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\base-server.js:895:17)\n    at async C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:371:20\n    at async Span.traceAsyncFn (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\trace\\trace.js:157:20)\n    at async DevServer.handleRequest (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\dev\\next-dev-server.js:368:24)\n    at async invokeRender (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\router-server.js:235:21)\n    at async handleRequest (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\router-server.js:426:24)\n    at async requestHandlerImpl (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\router-server.js:450:13)\n    at async Server.requestListener (C:\\Users\\<USER>\\Desktop\\harunacommunity\\node_modules\\next\\dist\\server\\lib\\start-server.js:158:13)"},"timestamp":"2025-05-20 23:14:20"}
{"level":"info","message":"Processing program form update","metadata":{"method":"PUT","programId":"6821d133cfbc52682ef2f6d0","service":"program-form-api"},"timestamp":"2025-05-20 23:16:33"}
{"level":"info","message":"Program form updated successfully","metadata":{"formId":"6821d170cfbc52682ef2f6de","programId":"6821d133cfbc52682ef2f6d0","service":"program-form-api"},"timestamp":"2025-05-20 23:16:33"}
{"level":"info","message":"Received application submission request","metadata":{"ip":"::1","service":"api/applicant"},"timestamp":"2025-05-20 23:17:47"}
{"level":"error","message":"Cloudinary upload failed","metadata":{"error":"Must supply api_key","fieldLabel":"field-1","fileName":"fauziya script.pdf","service":"api/applicant"},"timestamp":"2025-05-20 23:17:47"}
{"level":"info","message":"Form data validation passed","metadata":{"service":"api/applicant"},"timestamp":"2025-05-20 23:17:47"}
{"level":"info","message":"Application submitted successfully","metadata":{"applicantId":"682cff8bb2f4346ff6c4e531","programId":"6821d133cfbc52682ef2f6d0","service":"api/applicant"},"timestamp":"2025-05-20 23:17:47"}
{"level":"info","message":"Received application submission request","metadata":{"ip":"::1","service":"api/applicant"},"timestamp":"2025-05-20 23:25:50"}
{"level":"info","message":"Processing form data with potential file uploads","metadata":{"service":"api/applicant"},"timestamp":"2025-05-20 23:25:50"}
{"level":"info","message":"File uploaded to Cloudinary successfully","metadata":{"fieldLabel":"field-1","fileName":"1747779950916_chapter3_U2.pdf","fileUrl":"https://res.cloudinary.com/dhnnf0obb/image/upload/v1747779953/applicants/6821d133cfbc52682ef2f6d0/1747779950916_chapter3_U2.pdf","resourceType":"image","service":"api/applicant"},"timestamp":"2025-05-20 23:25:55"}
{"level":"info","message":"Form data validation passed","metadata":{"service":"api/applicant"},"timestamp":"2025-05-20 23:25:55"}
{"level":"info","message":"Application submitted successfully","metadata":{"applicantId":"682d0173b2f4346ff6c4e54b","programId":"6821d133cfbc52682ef2f6d0","service":"api/applicant"},"timestamp":"2025-05-20 23:25:55"}
{"level":"info","message":"Received application submission request","metadata":{"ip":"::1","service":"api/applicant"},"timestamp":"2025-05-20 23:31:54"}
{"level":"info","message":"Processing form data with potential file uploads","metadata":{"service":"api/applicant"},"timestamp":"2025-05-20 23:31:54"}
{"level":"info","message":"File uploaded to Cloudinary successfully","metadata":{"fieldLabel":"field-1","fileName":"1747780314582_developed_by.jpg","fileUrl":"https://res.cloudinary.com/dhnnf0obb/image/upload/v1747780316/applicants/6821d133cfbc52682ef2f6d0/1747780314582_developed_by.jpg","resourceType":"image","service":"api/applicant"},"timestamp":"2025-05-20 23:31:58"}
{"level":"info","message":"Form data validation passed","metadata":{"service":"api/applicant"},"timestamp":"2025-05-20 23:31:58"}
{"level":"info","message":"Application submitted successfully","metadata":{"applicantId":"682d02deb2f4346ff6c4e56e","programId":"6821d133cfbc52682ef2f6d0","service":"api/applicant"},"timestamp":"2025-05-20 23:31:58"}
