import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import Applicant from "@/models/Applicant";
import { withAuth } from "@/middleware/authMiddleware";
import { rateLimit } from "@/lib/ratelimit";
import { createLogger } from "@/lib/logger";

const logger = createLogger({ service: "reject-applicant" });

const limiter = rateLimit({
  windowMs: 60 * 1000,
  max: 30,
  keyPrefix: "patchRejectApplicant"
});

async function handler(req) {
  const rateCheck = await limiter(req);
  if (!rateCheck.success) return rateCheck.response;

  try {
    await dbConnect();

    const { applicantId, reviewerId } = await req.json();

    if (!applicantId) {
      logger.warn("Missing applicantId");
      return NextResponse.json({ error: "Applicant ID is required" }, { status: 400 });
    }

    if (!reviewerId) {
      logger.warn("Missing reviewerId");
      return NextResponse.json({ error: "Reviewer ID is required" }, { status: 400 });
    }

    const applicant = await Applicant.findById(applicantId);

    if (!applicant) {
      logger.warn("Applicant not found", { applicantId });
      return NextResponse.json({ error: "Applicant not found" }, { status: 404 });
    }

    // Prevent rejection if applicant status is 'selected'
    if (applicant.status === "selected") {
      logger.warn("Cannot reject applicant with status 'selected'", { applicantId });
      return NextResponse.json({ error: "Cannot reject applicant who is already selected" }, { status: 400 });
    }

    // If reviewerId is already set and doesn't match the current reviewer
    if (applicant.reviewerId && applicant.reviewerId.toString() !== reviewerId) {
      logger.warn("Reviewer mismatch", { applicantId, attemptedReviewerId: reviewerId });
      return NextResponse.json({ error: "You are not authorized to reject this applicant" }, { status: 403 });
    }

    applicant.status = "rejected";
    applicant.reviewerId = reviewerId;
    await applicant.save();

    logger.info("Applicant rejected", {
      applicantId,
      reviewerId
    });

    return NextResponse.json({
      message: "Applicant rejected successfully",
      applicant
    });
  } catch (error) {
    logger.error("Error rejecting applicant", {
      error: error.message,
      stack: error.stack
    });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export const PATCH = withAuth(handler);
