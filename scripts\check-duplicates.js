const mongoose = require('mongoose');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/haruna';

// Main function to check for duplicates
async function checkDuplicates() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully!');

    const db = mongoose.connection.db;
    const collection = db.collection('applicants');

    console.log('🔍 Analyzing duplicate phone numbers...\n');
    
    // Find duplicates using aggregation
    const duplicates = await collection.aggregate([
      {
        $match: {
          "formData.phonenumber": { $exists: true, $ne: null, $ne: "" }
        }
      },
      {
        $group: {
          _id: {
            programId: "$programId",
            phone: "$formData.phonenumber"
          },
          docs: { 
            $push: {
              id: "$_id",
              createdAt: "$createdAt",
              name: "$formData.name",
              status: "$status"
            }
          },
          count: { $sum: 1 }
        }
      },
      {
        $match: {
          count: { $gt: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]).toArray();

    if (duplicates.length === 0) {
      console.log('✅ No duplicate phone numbers found!');
    } else {
      console.log(`❌ Found ${duplicates.length} sets of duplicate phone numbers:\n`);
      
      let totalDuplicateEntries = 0;
      
      duplicates.forEach((duplicate, index) => {
        const extraEntries = duplicate.count - 1;
        totalDuplicateEntries += extraEntries;
        
        console.log(`${index + 1}. Phone: ${duplicate._id.phone}`);
        console.log(`   Program ID: ${duplicate._id.programId}`);
        console.log(`   Total entries: ${duplicate.count} (${extraEntries} duplicates)`);
        console.log('   Entries:');
        
        duplicate.docs.forEach((doc, docIndex) => {
          const createdDate = new Date(doc.createdAt).toLocaleString();
          console.log(`     ${docIndex + 1}. ID: ${doc.id} | Name: ${doc.name || 'N/A'} | Status: ${doc.status} | Created: ${createdDate}`);
        });
        console.log('');
      });
      
      console.log(`📊 Summary:`);
      console.log(`   Duplicate phone number sets: ${duplicates.length}`);
      console.log(`   Total extra entries to remove: ${totalDuplicateEntries}`);
    }

    // Check for global phone duplicates (across all programs)
    console.log('\n🌍 Checking for phone numbers used across multiple programs...\n');
    
    const crossProgramDuplicates = await collection.aggregate([
      {
        $match: {
          "formData.phonenumber": { $exists: true, $ne: null, $ne: "" }
        }
      },
      {
        $group: {
          _id: "$formData.phonenumber",
          programs: { 
            $addToSet: {
              programId: "$programId",
              applicantId: "$_id",
              name: "$formData.name",
              createdAt: "$createdAt"
            }
          },
          count: { $sum: 1 }
        }
      },
      {
        $match: {
          "programs.1": { $exists: true } // Has more than one program
        }
      },
      {
        $sort: { count: -1 }
      }
    ]).toArray();

    if (crossProgramDuplicates.length === 0) {
      console.log('✅ No phone numbers found across multiple programs');
    } else {
      console.log(`⚠️  Found ${crossProgramDuplicates.length} phone numbers used across multiple programs:\n`);
      
      crossProgramDuplicates.forEach((duplicate, index) => {
        console.log(`${index + 1}. Phone: ${duplicate._id}`);
        console.log(`   Used in ${duplicate.programs.length} programs:`);
        
        duplicate.programs.forEach((program, progIndex) => {
          const createdDate = new Date(program.createdAt).toLocaleString();
          console.log(`     ${progIndex + 1}. Program: ${program.programId} | Name: ${program.name || 'N/A'} | Created: ${createdDate}`);
        });
        console.log('');
      });
    }

    // General statistics
    const totalApplicants = await collection.countDocuments();
    const applicantsWithPhone = await collection.countDocuments({
      "formData.phonenumber": { $exists: true, $ne: null, $ne: "" }
    });
    const uniquePhones = await collection.distinct("formData.phonenumber", {
      "formData.phonenumber": { $exists: true, $ne: null, $ne: "" }
    });

    console.log('\n📊 General Statistics:');
    console.log(`   Total applicants: ${totalApplicants}`);
    console.log(`   Applicants with phone numbers: ${applicantsWithPhone}`);
    console.log(`   Unique phone numbers: ${uniquePhones.length}`);
    console.log(`   Duplicate entries: ${applicantsWithPhone - uniquePhones.length}`);

  } catch (error) {
    console.error('❌ Error checking duplicates:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  checkDuplicates();
}

module.exports = { checkDuplicates };
