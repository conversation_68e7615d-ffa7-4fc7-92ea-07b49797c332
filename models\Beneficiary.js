import mongoose from "mongoose";

const BeneficiarySchema = new mongoose.Schema(
  {
    applicantId: {
      type: String, // Stores the ID of the approved applicant
      required: true,
    },
    programId: {
      type: String, // Stores the ID of the related charity program
      required: true,
    },
    formPhase: {
      type: Number, // Stores the phase in which the applicant was selected
      required: true,
    },
    formData: {
      type: Map,
      of: mongoose.Schema.Types.Mixed, // Stores the applicant's submitted form data
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  { timestamps: true }
);

export default mongoose.models.Beneficiary ||
  mongoose.model("Beneficiary", BeneficiarySchema);
