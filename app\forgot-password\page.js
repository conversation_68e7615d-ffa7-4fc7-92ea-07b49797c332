"use client";

import { useState } from "react";
import { toast } from "sonner";
import axios from "axios";
import { z } from "zod";
import Header1 from "@/components/Header1";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

// Create email validation schema
const emailSchema = z.object({
  email: z.string().email("Please enter a valid email address")
});

export default function ForgotPassword() {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [validationError, setValidationError] = useState("");

  const handleSubmit = async (e) => {
    e.preventDefault();
    setValidationError("");
    
    // Validate email with Zod
    try {
      emailSchema.parse({ email });
    } catch (error) {
      if (error instanceof z.ZodError) {
        setValidationError(error.errors[0].message);
        return;
      }
    }

    try {
      setLoading(true);
      
      // Using axios instead of fetch
      const response = await axios.post("/api/auth/request-reset", { email }, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      
      toast.success(response.data.message || "Reset link sent!");
    } catch (error) {
      // Axios error handling
      const errorMessage = error.response?.data?.error || "Something went wrong, please try again later.";
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header1 />
      <div className="flex-1 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-xl font-bold">Forgot Password</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit}>
              <div className="space-y-4">
                <div>
                  <Input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    className={validationError ? "border-red-500" : ""}
                  />
                  {validationError && (
                    <p className="text-red-500 text-sm mt-1">{validationError}</p>
                  )}
                </div>
                <Button 
                  className="w-full bg-blue-600 hover:bg-blue-700" 
                  type="submit"
                  disabled={loading}
                >
                  {loading ? "Sending..." : "Send Reset Link"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}