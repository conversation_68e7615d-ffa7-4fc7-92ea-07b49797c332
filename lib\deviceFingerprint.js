/**
 * Device Fingerprinting Utility
 * Generates a unique fingerprint based on browser and device characteristics
 */

// Helper function to hash a string using a simple hash algorithm
function simpleHash(str) {
  let hash = 0;
  if (str.length === 0) return hash;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

// Get screen information
function getScreenInfo() {
  return {
    width: screen.width,
    height: screen.height,
    colorDepth: screen.colorDepth,
    pixelDepth: screen.pixelDepth,
    availWidth: screen.availWidth,
    availHeight: screen.availHeight
  };
}

// Get timezone information
function getTimezoneInfo() {
  return {
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    timezoneOffset: new Date().getTimezoneOffset()
  };
}

// Get language information
function getLanguageInfo() {
  return {
    language: navigator.language,
    languages: navigator.languages ? navigator.languages.join(',') : ''
  };
}

// Get platform information
function getPlatformInfo() {
  return {
    platform: navigator.platform,
    userAgent: navigator.userAgent,
    vendor: navigator.vendor || '',
    cookieEnabled: navigator.cookieEnabled,
    doNotTrack: navigator.doNotTrack || 'unspecified'
  };
}

// Get canvas fingerprint
function getCanvasFingerprint() {
  try {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Set canvas size
    canvas.width = 200;
    canvas.height = 50;
    
    // Draw some text and shapes
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillStyle = '#f60';
    ctx.fillRect(125, 1, 62, 20);
    ctx.fillStyle = '#069';
    ctx.fillText('Device fingerprint test 🔒', 2, 15);
    ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
    ctx.fillText('Device fingerprint test 🔒', 4, 17);
    
    return canvas.toDataURL();
  } catch (e) {
    return 'canvas_not_supported';
  }
}

// Get WebGL fingerprint
function getWebGLFingerprint() {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    if (!gl) return 'webgl_not_supported';
    
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
    const vendor = debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : '';
    const renderer = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : '';
    
    return {
      vendor,
      renderer,
      version: gl.getParameter(gl.VERSION),
      shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
    };
  } catch (e) {
    return 'webgl_error';
  }
}

// Get audio context fingerprint
function getAudioFingerprint() {
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const analyser = audioContext.createAnalyser();
    const gainNode = audioContext.createGain();
    const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);
    
    oscillator.type = 'triangle';
    oscillator.frequency.value = 10000;
    
    gainNode.gain.value = 0;
    
    oscillator.connect(analyser);
    analyser.connect(scriptProcessor);
    scriptProcessor.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.start(0);
    
    const frequencyData = new Uint8Array(analyser.frequencyBinCount);
    analyser.getByteFrequencyData(frequencyData);
    
    oscillator.stop();
    audioContext.close();
    
    return Array.from(frequencyData).slice(0, 30).join(',');
  } catch (e) {
    return 'audio_not_supported';
  }
}

// Get hardware concurrency
function getHardwareInfo() {
  return {
    hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
    deviceMemory: navigator.deviceMemory || 'unknown',
    maxTouchPoints: navigator.maxTouchPoints || 0
  };
}

// Main function to generate device fingerprint
export function generateDeviceFingerprint() {
  if (typeof window === 'undefined') {
    // Server-side rendering - return null
    return null;
  }

  try {
    const components = {
      screen: getScreenInfo(),
      timezone: getTimezoneInfo(),
      language: getLanguageInfo(),
      platform: getPlatformInfo(),
      canvas: getCanvasFingerprint(),
      webgl: getWebGLFingerprint(),
      audio: getAudioFingerprint(),
      hardware: getHardwareInfo()
    };

    // Create a string representation of all components
    const fingerprintString = JSON.stringify(components);
    
    // Generate hash of the fingerprint
    const fingerprint = simpleHash(fingerprintString);
    
    return {
      fingerprint,
      components, // Include components for debugging if needed
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Error generating device fingerprint:', error);
    return {
      fingerprint: 'error_' + Date.now(),
      error: error.message,
      timestamp: Date.now()
    };
  }
}

// Function to get a simple fingerprint (just the hash)
export function getDeviceFingerprint() {
  const result = generateDeviceFingerprint();
  return result ? result.fingerprint : null;
}
