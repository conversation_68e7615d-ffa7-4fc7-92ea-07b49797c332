// components/profile/SecurityForm.jsx
"use client"
import { useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { KeyIcon } from "lucide-react"
import { toast } from "sonner"
import { z } from "zod"

// Password change validation schema
const passwordChangeSchema = z
  .object({
    currentPassword: z.string().min(1, "Current password is required"),
    newPassword: z.string().min(6, "New password must be at least 6 characters"),
    confirmPassword: z.string().min(6, "Confirm password must be at least 6 characters"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  })

export default function SecurityForm({ api }) {
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })
  
  // Form validation errors
  const [passwordErrors, setPasswordErrors] = useState({})

  // Change password mutation
  const changePasswordMutation = useMutation({
    mutationFn: (data) => {
      // We're deliberately removing confirmPassword here as the API likely doesn't need it
      const { currentPassword, newPassword } = data;
      return api.changePassword({ currentPassword, newPassword });
    },
    onSuccess: () => {
      toast.success("Password changed", {
        description: "Your password has been updated successfully",
      })
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      })
      setPasswordErrors({})
    },
    onError: (error) => {
      console.error("Password change error:", error);
      
      if (error.name === "ZodError") {
        // Handle Zod validation errors
        const formattedErrors = {}
        error.errors.forEach((err) => {
          formattedErrors[err.path[0]] = err.message
        })
        setPasswordErrors(formattedErrors)
      } else if (error.response?.data?.error) {
        // API error with specific message
        toast.error("Password change failed", {
          description: error.response.data.error
        })
      } else {
        // Generic error
        toast.error("Password change failed", {
          description: "Could not update your password. Please try again later."
        })
      }
    },
  })

  // Handle password form input changes
  const handlePasswordChange = (e) => {
    const { name, value } = e.target
    setPasswordData((prev) => ({ ...prev, [name]: value }))

    // Clear error for this field when user starts typing
    if (passwordErrors[name]) {
      setPasswordErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      })
    }

    // Clear confirm password error when either password field changes
    if ((name === "newPassword" || name === "confirmPassword") && passwordErrors.confirmPassword) {
      setPasswordErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.confirmPassword;
        return newErrors;
      })
    }
  }

  // Change password
  const changePassword = (e) => {
    e.preventDefault()

    try {
      // Validate password data before submitting
      passwordChangeSchema.parse(passwordData)
      
      // If validation passes, proceed with the mutation
      changePasswordMutation.mutate(passwordData)
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Format and display validation errors
        const formattedErrors = {}
        error.errors.forEach((err) => {
          formattedErrors[err.path[0]] = err.message
        })
        setPasswordErrors(formattedErrors)
        
        toast.error("Validation failed", {
          description: Object.values(formattedErrors)[0] || "Please check the form for errors"
        })
      }
    }
  }

  return (
    <div className="max-w-md mx-auto bg-card rounded-xl shadow-sm p-6 border">
      <h2 className="text-xl font-semibold mb-4 flex items-center">
        <KeyIcon className="mr-2 h-5 w-5 text-white" />
        Change Password
      </h2>

      <form onSubmit={changePassword} className="space-y-4">
        <div>
          <label htmlFor="currentPassword" className="block text-sm font-medium mb-1">
            Current Password
          </label>
          <Input
            id="currentPassword"
            name="currentPassword"
            type="password"
            value={passwordData.currentPassword}
            onChange={handlePasswordChange}
            className={`w-full ${passwordErrors.currentPassword ? "border-red-500" : ""}`}
          />
          {passwordErrors.currentPassword && (
            <p className="text-red-500 text-xs mt-1">{passwordErrors.currentPassword}</p>
          )}
        </div>

        <div>
          <label htmlFor="newPassword" className="block text-sm font-medium mb-1">
            New Password
          </label>
          <Input
            id="newPassword"
            name="newPassword"
            type="password"
            value={passwordData.newPassword}
            onChange={handlePasswordChange}
            className={`w-full ${passwordErrors.newPassword ? "border-red-500" : ""}`}
          />
          {passwordErrors.newPassword && (
            <p className="text-red-500 text-xs mt-1">{passwordErrors.newPassword}</p>
          )}
          <p className="text-xs text-muted-foreground mt-1">Must be at least 6 characters</p>
        </div>

        <div>
          <label htmlFor="confirmPassword" className="block text-sm font-medium mb-1">
            Confirm New Password
          </label>
          <Input
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            value={passwordData.confirmPassword}
            onChange={handlePasswordChange}
            className={`w-full ${passwordErrors.confirmPassword ? "border-red-500" : ""}`}
          />
          {passwordErrors.confirmPassword && (
            <p className="text-red-500 text-xs mt-1">{passwordErrors.confirmPassword}</p>
          )}
        </div>

        <div className="mt-6">
          <Button
            type="submit"
            disabled={changePasswordMutation.isPending}
            className="w-full text-white bg-slate-700 hover:bg-slate-600"
          >
            {changePasswordMutation.isPending ? (
              <>
                <div className="w-4 h-4 border-2 border-t-white border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mr-2"></div>
                Updating...
              </>
            ) : (
              "Change Password"
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}