"use client";
import DrawerLayout from "@/components/DrawerLayout";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import axios from "axios";
import { toast } from "sonner";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";
import programSchema from "@/utils/programSchema";
import BackButton from "@/components/Back-Button";
// Import Shadcn UI components
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import AdminOnly from "@/components/AdminOnly";
import AuthOnly from "@/components/AuthOnly";
export default function ProgramForm() {
  const [submitting, setSubmitting] = useState(false);
  const router = useRouter();
  const queryClient = useQueryClient();

  return (
    <AuthOnly>
      <AdminOnly>
    <DrawerLayout>
      <div className="min-h-screen px-4 pb-4 pt-2">
        <BackButton/>
        <Card className="w-full md:max-w-2xl mx-auto mt-4 mb-8">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center">Create Program</CardTitle>
          </CardHeader>
          <CardContent>
            <Formik
              initialValues={{
                name: "",
                description: "",
                startDate: "",
                endDate: "",
                venue: "",
                maxBeneficiaries: "",
              }}
              validationSchema={toFormikValidationSchema(programSchema)}
              onSubmit={async (values, { resetForm }) => {
                try {
                  setSubmitting(true);
                  const payload = {
                    ...values,
                    linkName: values.name.toLowerCase().replace(/\s+/g, "-"),
                  };
              
                  const res = await axios.post("/api/programs/create", payload);
              
                  // ✅ Get the newly created program ID from response
                  const programId = res.data.id;
              
                  // ✅ Invalidate and refetch the programs query
                  await queryClient.invalidateQueries(["programsSummary"]);
                  await queryClient.invalidateQueries(["programs"]);
              
                  toast.success("✅ Program created successfully! 🎉");
                  resetForm();
              
                  // ✅ Redirect to program details page
                  router.push(`/programs/programdetails/${programId}`);
                } catch (error) {
                  console.error("Error creating program:", error);
                  if (error.response) {
                    const { status, data } = error.response;
                    if (status === 400 && data.error) {
                      toast.error("🚨 Validation error: Please check the form fields.");
                    } else if (status === 409) {
                      toast.warning("⚠️ A program with this name already exists!");
                    } else {
                      toast.error("❌ Failed to create program. Please try again later.");
                    }
                  } else {
                    toast.error("⚠️ Network error. Please check your internet connection.");
                  }
                } finally {
                  setSubmitting(false);
                }
              }}
            >
              {({ isSubmitting, handleChange, handleBlur, values }) => (
                <Form className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Program Name</Label>
                    <Input
                      id="name"
                      name="name"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.name}
                      className= "bg-background"
                    />
                    <ErrorMessage name="name" component="p" className="text-red-500 text-sm" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      name="description"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.description}
                      className="min-h-32 bg-background"
                    />
                    <ErrorMessage name="description" component="p" className="text-red-500 text-sm" />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="startDate">Start Date</Label>
                      <Input
                        id="startDate"
                        name="startDate"
                        type="date"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.startDate}
                        className= "bg-background"
                      />
                      <ErrorMessage name="startDate" component="p" className="text-red-500 text-sm" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="endDate">End Date</Label>
                      <Input
                        id="endDate"
                        name="endDate"
                        type="date"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.endDate}
                        className= "bg-background"
                      />
                      <ErrorMessage name="endDate" component="p" className="text-red-500 text-sm" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="venue">Venue</Label>
                      <Input
                        id="venue"
                        name="venue"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.venue}
                        className= "bg-background"
                      />
                      <ErrorMessage name="venue" component="p" className="text-red-500 text-sm" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="maxBeneficiaries">Max Beneficiaries</Label>
                      <Input
                        id="maxBeneficiaries"
                        name="maxBeneficiaries"
                        type="number"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.maxBeneficiaries}
                        className= "bg-background"
                      />
                      <ErrorMessage name="maxBeneficiaries" component="p" className="text-red-500 text-sm" />
                    </div>
                  </div>

                  <Button type="submit" disabled={isSubmitting} className="w-full font-bold  bg-blue-900 text-white mt-4">
                    {isSubmitting ? "Submitting..." : "Create Program"}
                  </Button>
                </Form>
              )}
            </Formik>
          </CardContent>
        </Card>
      </div>
    </DrawerLayout></AdminOnly></AuthOnly>
  );
}