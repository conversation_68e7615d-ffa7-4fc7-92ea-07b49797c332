import React from 'react'

function <PERSON><PERSON>leton() {
  return (
    <div className="p-4 w-full">
    

    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {[...Array(6)].map((_, index) => (
        <div key={index} className="relative bg-card w-full border border-slate-700 p-4 rounded-2xl">
          <div className="absolute top-2 skeleton    w-16 h-6"></div>

          <div className="card-body text-white">
            <div className="skeleton  h-5 w-3/4 mb-2"></div>
            <div className="divider w-full -mt-3"/>

            <div className="flex items-center gap-2 -mt-3 mb-2">
              <div className="skeleton   h-5 w-32"></div>
            </div>

            <div className="flex items-center gap-2 mb-2">
              <div className="skeleton   h-5 w-32"></div>
            </div>

            <div className="flex items-center gap-2">
              <div className="skeleton   h-5 w-32"></div>
            </div>

            <div className="flex text-sm mb-5 justify-center text-background mt-2">
              <div className="skeleton  h-15 w-28"></div>
              <div className="skeleton   h-15 w-28"></div>
            </div>

            <div className="absolute bottom-3 right-3">
              <div className="skeleton   h-8 w-24"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
  )
}

export default Pskeleton