// lib/ratelimit.js

const requestLogs = {};

export function rateLimit({ windowMs, max, keyPrefix }) {
  return async function (req) {
    const ip =
      req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() ||
      "unknown";

    if (!ip) {
      return {
        success: false,
        response: new Response(
          JSON.stringify({ error: "Unable to detect client IP" }),
          { status: 400 }
        ),
      };
    }

    const key = `${keyPrefix || "default"}:${ip}`;
    const currentTime = Date.now();

    if (!requestLogs[key]) {
      requestLogs[key] = [];
    }

    // Remove outdated timestamps
    requestLogs[key] = requestLogs[key].filter(
      (timestamp) => currentTime - timestamp < windowMs
    );

    if (requestLogs[key].length >= max) {
      return {
        success: false,
        response: new Response(
          JSON.stringify({ message: "Too many requests. Please try again later." }),
          { status: 429 }
        ),
      };
    }

    requestLogs[key].push(currentTime);

    return { success: true };
  };
}
